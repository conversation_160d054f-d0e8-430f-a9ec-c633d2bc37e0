<template>
	<view class="custom-tabbar">
		<view class="tabbar-item" @click="switchTab(0)" :class="{ active: currentTab === 0 }">
			<view class="tab-icon"><i class="fas fa-home"></i></view>
			<text class="tab-text">广场</text>
		</view>
		
		<view class="tabbar-item center-item" @click="switchTab(1)">
			<view class="center-btn" :class="{ active: currentTab === 1 }">
				<text class="center-icon">+</text>
			</view>
		</view>
		
		<view class="tabbar-item" @click="switchTab(2)" :class="{ active: currentTab === 2 }">
			<i class="tab-icon fas fa-user"></i>
			<text class="tab-text">我的</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomTabbar',
		props: {
			current: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				currentTab: 0
			}
		},
		watch: {
			current: {
				handler(newVal) {
					this.currentTab = newVal
				},
				immediate: true
			}
		},
		methods: {
			switchTab(index) {
				if (this.currentTab === index) return

				const urls = [
					'/pages/index/index',
					'/pages/record-new/record-new',
					'/pages/profile/profile'
				]

				// 使用reLaunch确保页面栈清理
				uni.reLaunch({
					url: urls[index]
				})
			}
		}
	}
</script>

<style>
	.custom-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		background: #FFFFFF;
		border-top: 1rpx solid #E9ECEF;
		display: flex;
		align-items: center;
		justify-content: space-around;
		z-index: 1000;
		box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);
		/* 为iOS设备安全区域预留底部空间 */
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100%;
		transition: all 0.2s ease;
	}

	.tabbar-item:active {
		transform: scale(0.95);
	}

	.tab-icon {
		font-size: 44rpx;
		margin-bottom: 8rpx;
		opacity: 0.6;
		color: #6C757D;
		transition: all 0.2s ease;
	}

	/* 首页图标特殊颜色 */
	.tabbar-item:first-child .tab-icon {
		color: #2196F3;
	}

	/* 个人中心图标特殊颜色 */
	.tabbar-item:last-child .tab-icon {
		color: #9C27B0;
	}

	.tab-text {
		font-size: 20rpx;
		color: #ADB5BD;
		font-weight: 500;
		transition: all 0.2s ease;
	}

	.tabbar-item.active .tab-icon {
		opacity: 1;
		color: #FF6B35;
	}

	.tabbar-item.active .tab-text {
		color: #FF6B35;
	}

	/* 中间加号按钮 */
	.center-item {
		position: relative;
		top: -20rpx;
	}

	.center-btn {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background: #FF6B35;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
		transition: all 0.2s ease;
	}

	.center-btn:active {
		transform: scale(0.95);
		background: #E55A2B;
	}

	.center-btn.active {
		background: #E55A2B;
		box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
	}

	.center-icon {
		font-size: 64rpx;
		color: #FFFFFF;
		font-weight: 300;
		line-height: 1;
	}
</style>

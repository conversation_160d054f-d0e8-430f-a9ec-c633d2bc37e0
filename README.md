# 旅行记录 Uni-App 项目 - AI协作开发文档

## 项目概览

**框架**: uni-app  
**目标平台**: 微信小程序（优先），兼容H5和其他小程序平台  
**主要功能**: 智能旅行记录管理，支持多种AI输入方式

## 技术架构

### 核心数据结构
```javascript
// 全局数据结构 - 存储在 localStorage 'travelData'
{
  travel: [
    {
      city: "城市名",
      points: [
        {
          place: "地点名称",
          arriveTime: "到达时间",
          duration: "停留时长", 
          desc: "描述",
          media: ["图片路径数组"],
          experience: 0, // 体验评分 0-5
          expenses: [
            {
              category: "分类",
              amount: 0,
              note: "备注"
            }
          ],
          lat: null, // 纬度
          lng: null  // 经度
        }
      ]
    }
  ]
}
```

### 页面架构
- `pages/index/index.vue` - 主页，显示旅行列表
- `pages/record/record.vue` - 记录页面，管理单个旅行的所有点
- `pages/city/city.vue` - 城市选择页面
- `pages/smart-input/smart-input.vue` - **核心智能输入页面**
- `pages/detail/detail.vue` - 详情页面
- `pages/stats/stats.vue` - 统计页面
- `pages/share/share.vue` - 分享页面
- `pages/search/search.vue` - 搜索页面
- `pages/map/map.vue` - 地图查看页面

## 已实现功能模块

### 1. 智能输入系统 (`smart-input.vue`)

**四种输入模式**:
1. **位置选择** (`currentMethod: 'location'`) - 地图POI选择
2. **文字录入** (`currentMethod: 'text'`) - AI解析文本输入
3. **语音录入** (`currentMethod: 'voice'`) - 语音转文字录入
4. **识图录入** (`currentMethod: 'image'`) - 拍照识别店铺

**地图集成**:
- 使用uni-app标准 `<map>` 组件（跨平台兼容）
- 集成高德地图POI搜索API
- 高德Web API Key: 通过配置文件管理（见config.js）
- 实时POI搜索，支持地图拖拽更新

**关键实现**:
```javascript
// POI搜索方法
searchNearbyPOIs() {
  uni.request({
    url: 'https://restapi.amap.com/v3/place/around',
    data: {
      key: this.config.amapKey, // 使用配置文件中的API Key
      location: `${this.mapCenter.lng},${this.mapCenter.lat}`,
      radius: 1000,
      offset: 20,
      page: 1,
      extensions: 'base'
    },
    success: (res) => {
      if (res.data.status === '1') {
        this.nearbyPOIs = res.data.pois;
      }
    }
  });
}

// 地图区域变化监听
onRegionChange(e) {
  if (e.type === 'end') {
    const mapContext = uni.createMapContext('smartInputMap', this);
    mapContext.getCenterLocation({
      success: (res) => {
        this.mapCenter = { lat: res.latitude, lng: res.longitude };
        this.searchNearbyPOIs();
      }
    });
  }
}
```

### 2. 数据流设计

**页面间数据传递**:
- `smart-input` → `record`: 通过 `uni.navigateBack()` + `getCurrentPages()` 传递数据
- 全局数据持久化: `uni.setStorageSync('travelData', this.travel)`

**数据操作模式**:
```javascript
// 获取数据
this.travel = uni.getStorageSync('travelData') || { travel: [] };

// 保存数据  
saveData() {
  uni.setStorageSync('travelData', this.travel);
}
```

### 3. 费用管理系统

**expenses数组结构**:
- 每个point支持多个expense项目
- 支持OCR收据识别（模拟实现）
- 按类别和日期聚合统计

### 4. 跨平台兼容性策略

**地图方案**:
- 使用uni-app标准map组件而非平台特定组件
- 数据源使用高德Web API（跨平台）
- 避免使用微信小程序专属API

**权限配置** (`manifest.json`):
```json
{
  "mp-weixin": {
    "requiredPrivateInfos": ["getLocation"],
    "permission": {
      "scope.userLocation": {
        "desc": "获取位置信息用于记录旅行轨迹"
      }
    }
  }
}
```

## 开发规范

### 1. 代码结构
- 使用Vue 3语法
- 数据响应式管理通过Vue的data和methods
- 异步操作使用async/await

### 2. 样式规范
- 使用uni-app标准样式单位（rpx）
- 响应式设计，适配不同屏幕尺寸
- 使用uni-ui组件库风格

### 3. API集成规范
- 统一使用 `uni.request()` 进行网络请求
- 错误处理使用 `uni.showToast()` 反馈
- 加载状态使用 `uni.showLoading()`

## 待实现功能

### 智能输入功能完善
1. **图像识别增强** - 完善店铺拍照识别并获取POI信息
2. **文本解析优化** - 完善AI解析自然语言描述提取结构化数据
3. **语音输入完善** - 集成真实语音识别API
4. **行程生成** - 基于用户偏好生成完整一日行程（新功能）

### 数据增强
- POI详细信息获取（营业时间、评分、图片）
- 路线规划和时间估算
- 天气信息集成

## 技术依赖

**核心依赖**:
- uni-app框架
- Vue 3
- 高德地图Web API
- 微信小程序基础库

**推荐工具**:
- HBuilderX（官方IDE）
- 微信开发者工具（调试）

## 配置管理

本项目采用创新的全局配置管理方案，解决了 uniapp + Vue3 环境中 ES6 import 的兼容性问题：

- **统一配置文件**: 所有配置集中在 `config.js`
- **自动全局挂载**: 配置自动挂载到 `uni.$appConfig`、`getApp().globalData.config` 等全局对象
- **多重备用机制**: 确保在任何环境下都能正确获取配置
- **避免硬编码**: 永远不在代码中硬编码配置信息

详细说明请参考：[UniApp + Vue3 全局配置管理指南](./docs/config/uniapp-vue3-global-config-guide.md)

## 数据安全
- 本地存储策略，数据存储在用户设备
- API Key通过配置文件管理，避免硬编码
- 无服务器依赖，纯前端实现

---

**重要提示**: 该项目优先考虑跨平台兼容性，避免使用平台特定API。所有地图相关功能基于uni-app标准组件实现。 
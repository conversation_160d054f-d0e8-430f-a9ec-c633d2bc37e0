<template>
  <view class="record-container">
    <!-- 城市列表 -->
    <view v-for="(city, cidx) in travel" :key="cidx" class="city-block">
      <view class="city-header">
        <input class="city-input" v-model="city.city" placeholder="城市名称" />
        <button size="mini" @click="goCityInfo(city.city)">
          <i class="fas fa-info-circle"></i> 城市信息
        </button>
        <button size="mini" type="warn" @click="removeCity(cidx)">
          <i class="fas fa-trash"></i> 删除
        </button>
      </view>
      <!-- 点位列表 -->
      <view class="section">
        <view class="section-title">旅行点位</view>
        <view v-for="(point, idx) in city.points" :key="idx" class="point-card">
          <view class="point-header">
            <view>第{{ idx+1 }}站</view>
            <button size="mini" type="warn" @click="removePoint(cidx, idx)">
              <i class="fas fa-times"></i>
            </button>
          </view>
          <view class="form-row">
            <button size="mini" @click="getLocation(cidx, idx)">
              <i class="fas fa-map-marker-alt"></i> 定位
            </button>
            <input class="input" v-model="point.place" placeholder="输入地点名称" />
            <button size="mini" @click="goSmartInput(cidx, idx)">
              <i class="fas fa-brain"></i> 智能录入
            </button>
          </view>
          <view class="form-row">
            <picker mode="time" :value="point.arriveTime" @change="e => setArriveTime(cidx, idx, e.detail.value)">
              <view class="picker">到达时间：{{ point.arriveTime || '请选择' }}</view>
            </picker>
            <input class="input" v-model="point.duration" placeholder="停留时长（分钟/小时）" />
          </view>
          <textarea class="input" v-model="point.desc" placeholder="描述" />
          <view class="form-row">
            <button size="mini" @click="chooseMedia(cidx, idx)">
              <i class="fas fa-camera"></i> 上传图片
            </button>
            <view class="media-list">
              <image v-for="(img, i) in point.media" :key="i" :src="img" class="media-img" />
            </view>
          </view>
          <input class="input" v-model="point.expense" placeholder="消费信息" />
          <view class="form-row">
            <view>体验度：</view>
            <slider min="-5" max="5" step="1" :value="point.experience" show-value />
          </view>
          <!-- 消费详情 -->
          <view class="expense-section">
            <view class="expense-title">消费详情</view>
            <view v-for="(expense, eidx) in point.expenses" :key="eidx" class="expense-item">
              <view class="expense-row">
                <input class="expense-input" v-model="expense.amount" type="number" placeholder="金额" />
                <picker :range="expenseCategories" @change="e => setExpenseCategory(cidx, idx, eidx, e.detail.value)">
                  <view class="expense-picker">{{ expense.category || '类别' }}</view>
                </picker>
                <input class="expense-input" v-model="expense.note" placeholder="备注" />
                <button size="mini" type="warn" @click="removeExpense(cidx, idx, eidx)">删除</button>
              </view>
            </view>
            <button size="mini" @click="addExpense(cidx, idx)">+ 添加消费</button>
            <button size="mini" @click="ocrExpense(cidx, idx)">拍照识别小票</button>
          </view>
        </view>
        <button class="add-point-btn" @click="addPoint(cidx)">
          <i class="fas fa-plus"></i> 添加点位
        </button>
      </view>
    </view>
    <button class="add-city-btn" @click="addCity">
      <i class="fas fa-plus"></i> 添加城市
    </button>
    <!-- 路线图占位 -->
    <view class="section">
      <view class="section-title">路线图</view>
      <view class="placeholder">（后续可自动生成路线图，当前为占位）</view>
    </view>
    <!-- 保存按钮 -->
    <button type="primary" class="submit-btn" @click="submit">
      <i class="fas fa-save"></i>
      <text class="btn-text">保存旅行记录</text>
    </button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      recordId: null, // 编辑模式下的记录ID
      travel: [
        {
          city: '',
          cityInfo: {},
          points: [
            { 
              place: '', 
              arriveTime: '', 
              duration: '', 
              desc: '', 
              media: [], 
              experience: 0,
              expenses: [],
              lat: null, // 添加纬度
              lng: null  // 添加经度
            }
          ]
        }
      ],
      expenseCategories: ['餐饮', '住宿', '市内交通', '大交通', '纪念品', '门票', '其他']
    }
  },
  onLoad(options) {
    // 如果有id参数，进入编辑模式，加载本地数据
    if (options.id) {
      this.recordId = options.id
      const records = uni.getStorageSync('records') || []
      const record = records.find(r => String(r.id) === String(options.id))
      if (record) {
        // 只加载travel部分，实际可根据你的数据结构调整
        this.travel = record.travel
      }
    }
  },
  methods: {
    addCity() {
      this.travel.push({
        city: '',
        cityInfo: {},
        points: [
          { 
            place: '', 
            arriveTime: '', 
            duration: '', 
            desc: '', 
            media: [], 
            experience: 0,
            expenses: [],
            lat: null, // 添加纬度
            lng: null  // 添加经度
          }
        ]
      })
    },
    removeCity(cidx) {
      this.travel.splice(cidx, 1)
    },
    goCityInfo(cityName) {
      if (!cityName) {
        uni.showToast({ title: '请先填写城市名称', icon: 'none' })
        return
      }
      uni.navigateTo({ url: `/pages/city/city?city=${cityName}` })
    },
    addPoint(cidx) {
      this.travel[cidx].points.push({ 
        place: '', 
        arriveTime: '', 
        duration: '', 
        desc: '', 
        media: [], 
        experience: 0,
        expenses: [],
        lat: null, // 添加纬度
        lng: null  // 添加经度
      })
    },
    removePoint(cidx, idx) {
      this.travel[cidx].points.splice(idx, 1)
    },
    getLocation(cidx, idx) {
      uni.getLocation({
        type: 'wgs84',
        success: (res) => {
          this.travel[cidx].points[idx].place = `经度${res.longitude}，纬度${res.latitude}`
        }
      })
    },
    chooseMedia(cidx, idx) {
      uni.chooseImage({
        count: 3,
        success: (res) => {
          this.travel[cidx].points[idx].media = this.travel[cidx].points[idx].media.concat(res.tempFilePaths)
        }
      })
    },
    setArriveTime(cidx, idx, value) {
      this.travel[cidx].points[idx].arriveTime = value
    },
    addExpense(cidx, idx) {
      this.travel[cidx].points[idx].expenses.push({
        amount: '',
        category: '',
        note: ''
      })
    },
    removeExpense(cidx, idx, eidx) {
      this.travel[cidx].points[idx].expenses.splice(eidx, 1)
    },
    setExpenseCategory(cidx, idx, eidx, categoryIndex) {
      this.travel[cidx].points[idx].expenses[eidx].category = this.expenseCategories[categoryIndex]
    },
    ocrExpense(cidx, idx) {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          uni.showLoading({ title: '识别中...' })
          // 这里调用后端OCR接口
          setTimeout(() => {
            uni.hideLoading()
            // 模拟OCR识别结果
            const ocrResult = {
              amount: '128.50',
              category: '餐饮',
              note: '午餐'
            }
            this.travel[cidx].points[idx].expenses.push(ocrResult)
            uni.showToast({ title: '识别成功', icon: 'success' })
          }, 2000)
        }
      })
    },
    goSmartInput(cidx, idx) {
      uni.navigateTo({ url: `/pages/smart-input/smart-input?cityId=${cidx}&pointIndex=${idx}` })
    },
    fillPointData(cityId, pointIndex, data) {
      // 智能录入返回的数据填充到对应点位
      if (this.travel[cityId] && this.travel[cityId].points[pointIndex]) {
        const point = this.travel[cityId].points[pointIndex]
        
        // 填充基本信息
        if (data.place) point.place = data.place
        if (data.arriveTime) point.arriveTime = data.arriveTime
        if (data.desc) point.desc = data.desc
        
        // 填充经纬度
        if (data.lat !== null && data.lat !== undefined) point.lat = data.lat
        if (data.lng !== null && data.lng !== undefined) point.lng = data.lng
        
        // 填充消费信息
        if (data.expenses && data.expenses.length > 0) {
          data.expenses.forEach(expense => {
            point.expenses.push({
              amount: expense.amount || '',
              category: expense.category || '其他',
              note: expense.note || '智能录入'
            })
          })
        }
        
        uni.showToast({ title: '智能录入成功', icon: 'success' })
      }
    },
    getAllExpenses() {
      const allExpenses = []
      this.travel.forEach((city, cidx) => {
        city.points.forEach((point, idx) => {
          point.expenses.forEach((expense, eidx) => {
            if (expense.amount && expense.category) {
              allExpenses.push({
                pointId: `${cidx}-${idx}`,
                place: point.place,
                category: expense.category,
                amount: Number(expense.amount),
                note: expense.note,
                date: point.arriveTime ? point.arriveTime.split(' ')[0] : '',
                source: 'point'
              })
            }
          })
        })
      })
      return allExpenses
    },
    submit() {
      // 校验
      if (!this.travel.length || this.travel.some(c => !c.city)) {
        uni.showToast({ title: '请填写所有城市名称', icon: 'none' })
        return
      }
      // 读取本地所有记录
      let records = uni.getStorageSync('records') || []
      if (this.recordId) {
        // 编辑模式，更新原有记录
        const idx = records.findIndex(r => String(r.id) === String(this.recordId))
        if (idx !== -1) {
          records[idx].travel = this.travel
        }
      } else {
        // 新建模式，生成新ID
        const newId = Date.now()
        records.push({
          id: newId,
          title: this.travel[0].city ? this.travel[0].city + '之旅' : '未命名旅途',
          desc: '',
          date: new Date().toISOString().split('T')[0],
          place: this.travel.map(c => c.city).join('、'),
          travel: JSON.parse(JSON.stringify(this.travel))
        })
      }
      uni.setStorageSync('records', records)
      uni.showToast({ title: '保存成功', icon: 'success' })
      setTimeout(() => {
        uni.switchTab ? uni.switchTab({ url: '/pages/index/index' }) : uni.reLaunch({ url: '/pages/index/index' })
      }, 500)
    }
  }
}
</script>

<style>
page {
	height: 100%;
	background: #FFFFFF;
}
.record-container {
	padding: 32rpx 24rpx 160rpx 24rpx;
	min-height: 100vh;
	background: #F8F9FA;
	box-sizing: border-box;
}
.city-block { margin-bottom: 48rpx; border: 2rpx solid #E9ECEF; border-radius: 20rpx; background: #FFFFFF; padding: 18rpx 12rpx; box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04); }
.city-header { display: flex; align-items: center; margin-bottom: 12rpx; }
.city-input { flex: 1; border: 1rpx solid #E9ECEF; border-radius: 12rpx; padding: 16rpx; font-size: 28rpx; margin-right: 12rpx; background: #fff; }
.section { margin-bottom: 32rpx; background: #fff; border-radius: 20rpx; padding: 24rpx 20rpx; border: 1rpx solid #E9ECEF; box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04); }
.section-title { font-size: 32rpx; font-weight: 600; margin-bottom: 16rpx; color: #FF6B35; line-height: 1.3; }
.form-row { display: flex; align-items: center; margin-bottom: 12rpx; }
.input { flex: 1; border: 1rpx solid #E9ECEF; border-radius: 12rpx; padding: 12rpx; font-size: 26rpx; margin-left: 12rpx; background: #fff; }
.picker { flex: 1; color: #333; font-size: 26rpx; padding: 8rpx 0; }
.point-card { margin-bottom: 24rpx; border: 1rpx solid #f9f9f9; border-radius: 12rpx; padding: 12rpx; background: #f9f9f9; }
.point-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 6rpx; }
.media-list { display: flex; }
.media-img { width: 80rpx; height: 80rpx; border-radius: 8rpx; margin-right: 8rpx; }
.add-point-btn { margin-top: 8rpx; width: 100%; font-size: 26rpx; border-radius: 20rpx; background: #FFE5DC; color: #FF6B35; }
.add-city-btn { margin-bottom: 24rpx; width: 100%; font-size: 30rpx; border-radius: 24rpx; background: #F8F9FA; color: #FF6B35; border: 1rpx solid #E9ECEF; }
.placeholder { color: #bbb; font-size: 24rpx; padding: 12rpx 0; }
.submit-btn {
	position: fixed;
	left: 50%;
	bottom: 48rpx;
	transform: translateX(-50%);
	width: 76vw;
	height: 96rpx;
	background: #FF6B35;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	border-radius: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(255, 107, 53, 0.25);
	z-index: 99;
	gap: 12rpx;
	border: none;
}
.submit-btn .fas {
	font-size: 28rpx;
}
.submit-btn .btn-text {
	font-size: 32rpx;
	font-weight: bold;
}
.expense-section { margin-top: 12rpx; padding: 12rpx; background: #EDF7F3; border-radius: 8rpx; }
.expense-title { font-size: 26rpx; font-weight: bold; margin-bottom: 8rpx; color: #05D592; }
.expense-item { margin-bottom: 8rpx; }
.expense-row { display: flex; align-items: center; }
.expense-input { flex: 1; border: 1rpx solid #EDF7F3; border-radius: 6rpx; padding: 8rpx; font-size: 24rpx; margin-right: 8rpx; background: #fff; }
.expense-picker { flex: 1; color: #333; font-size: 24rpx; padding: 6rpx 0; }
</style> 
<template>
  <view class="share-container">
    <!-- 只有没有id参数时才显示选择器 -->
    <view v-if="!onlyShareCurrent" class="section">
      <view class="section-title">选择要分享的旅行</view>
      <picker :range="recordList" @change="onRecordChange">
        <view class="picker">{{ selectedRecord ? selectedRecord.title : '请选择旅行记录' }}</view>
      </picker>
    </view>

    <!-- 生成内容预览 -->
    <view v-if="selectedRecord" class="section">
      <view class="section-title">生成内容预览</view>
      <view class="preview-card">
        <view class="preview-title">{{ selectedRecord.title }}</view>
        <view class="preview-desc">{{ selectedRecord.desc }}</view>
        <view class="preview-meta">{{ selectedRecord.date }} · {{ selectedRecord.place }}</view>
      </view>
    </view>

    <!-- 生成选项 -->
    <view v-if="selectedRecord" class="section">
      <view class="section-title">生成选项</view>
      <view class="option-list">
        <view class="option-item" @click="generateTimeline">
          <view class="option-title">时间轴</view>
          <view class="option-desc">按时间顺序展示所有点位</view>
        </view>
        <view class="option-item" @click="generateRoute">
          <view class="option-title">路线图</view>
          <view class="option-desc">展示旅行路线和地图</view>
        </view>
        <view class="option-item" @click="generateArticle">
          <view class="option-title">文字游记</view>
          <view class="option-desc">生成文字版游记内容</view>
        </view>
        <view class="option-item" @click="generateImage">
          <view class="option-title">长图</view>
          <view class="option-desc">生成图片版游记</view>
        </view>
      </view>
    </view>

    <!-- 分享选项 -->
    <view v-if="selectedRecord" class="section">
      <view class="section-title">分享到</view>
      <view class="share-list">
        <button class="share-btn" @click="shareToWechat">微信好友</button>
        <button class="share-btn" @click="shareToMoments">朋友圈</button>
        <button class="share-btn" @click="saveImage">保存图片</button>
        <button class="share-btn" @click="copyLink">复制链接</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      recordList: [],
      selectedRecord: null,
      onlyShareCurrent: false // 新增
    }
  },
  onLoad(options) {
    this.recordList = uni.getStorageSync('records') || []
    if (options.id) {
      const idx = this.recordList.findIndex(r => String(r.id) === String(options.id))
      if (idx !== -1) {
        this.selectedRecord = this.recordList[idx]
        this.onlyShareCurrent = true
      }
    }
  },
  onShow() {
    // 兼容直接进入分享页的情况
    if (!this.onlyShareCurrent) {
      this.recordList = uni.getStorageSync('records') || []
    }
    // 自动选中传递的id
    const id = this.$mp?.query?.id || (this.$route && this.$route.query && this.$route.query.id) || ''
    if (id) {
      const idx = this.recordList.findIndex(r => String(r.id) === String(id))
      if (idx !== -1) this.selectedRecord = this.recordList[idx]
    }
  },
  methods: {
    onRecordChange(e) {
      this.selectedRecord = this.recordList[e.detail.value]
    },
    generateTimeline() {
      if (!this.selectedRecord) return
      uni.showLoading({ title: '生成中...' })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({ title: '时间轴生成成功', icon: 'success' })
        // 这里可以跳转到时间轴预览页面
      }, 1000)
    },
    generateRoute() {
      if (!this.selectedRecord) return
      uni.showLoading({ title: '生成中...' })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({ title: '路线图生成成功', icon: 'success' })
        // 这里可以跳转到路线图预览页面
      }, 1000)
    },
    generateArticle() {
      if (!this.selectedRecord) return
      uni.showLoading({ title: '生成中...' })
      setTimeout(() => {
        uni.hideLoading()
        const article = this.generateArticleContent()
        uni.setClipboardData({
          data: article,
          success: () => {
            uni.showToast({ title: '文字游记已复制到剪贴板', icon: 'success' })
          }
        })
      }, 1000)
    },
    generateImage() {
      if (!this.selectedRecord) return
      uni.showLoading({ title: '生成中...' })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({ title: '长图生成成功', icon: 'success' })
        // 这里可以跳转到长图预览页面
      }, 1000)
    },
    generateArticleContent() {
      // 生成文字游记内容
      let content = `${this.selectedRecord.title}\n\n`
      content += `时间：${this.selectedRecord.date}\n`
      content += `地点：${this.selectedRecord.place}\n\n`
      
      if (this.selectedRecord.travel) {
        this.selectedRecord.travel.forEach((city, cidx) => {
          content += `【${city.city}】\n`
          city.points.forEach((point, idx) => {
            content += `${idx + 1}. ${point.place}\n`
            if (point.desc) content += `   ${point.desc}\n`
            if (point.arriveTime) content += `   到达时间：${point.arriveTime}\n`
            if (point.duration) content += `   停留时长：${point.duration}\n`
            if (point.expenses && point.expenses.length) {
              content += `   消费：${point.expenses.map(e => `${e.category}￥${e.amount}`).join('、')}\n`
            }
            content += '\n'
          })
        })
      }
      
      return content
    },
    shareToWechat() {
      uni.showToast({ title: '分享到微信好友', icon: 'success' })
      // 这里可以调用微信分享API
    },
    shareToMoments() {
      uni.showToast({ title: '分享到朋友圈', icon: 'success' })
      // 这里可以调用微信分享API
    },
    saveImage() {
      uni.showToast({ title: '图片已保存到相册', icon: 'success' })
      // 这里可以生成并保存图片
    },
    copyLink() {
      uni.setClipboardData({
        data: 'https://your-app.com/share?id=' + this.selectedRecord.id,
        success: () => {
          uni.showToast({ title: '链接已复制', icon: 'success' })
        }
      })
    }
  }
}
</script>

<style>
.share-container { padding: 32rpx 24rpx 120rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx 20rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #FF6B35; }
.picker { color: #333; font-size: 28rpx; padding: 12rpx 0; }
.preview-card { padding: 20rpx; background: #f9f9f9; border-radius: 12rpx; }
.preview-title { font-size: 32rpx; font-weight: bold; color: #222; }
.preview-desc { font-size: 28rpx; color: #666; margin: 8rpx 0; }
.preview-meta { font-size: 24rpx; color: #999; }
.option-list { display: flex; flex-wrap: wrap; }
.option-item { width: 50%; padding: 16rpx; box-sizing: border-box; }
.option-title { font-size: 28rpx; font-weight: bold; color: #333; }
.option-desc { font-size: 24rpx; color: #888; margin-top: 4rpx; }
.share-list { display: flex; flex-wrap: wrap; }
.share-btn { flex: 1; margin: 8rpx; font-size: 26rpx; border-radius: 24rpx; }
</style> 
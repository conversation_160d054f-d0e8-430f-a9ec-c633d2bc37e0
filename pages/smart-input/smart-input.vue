<template>
  <view class="smart-container">
    <!-- 录入方式选择 -->
    <view class="section">
      <view class="section-title">选择录入方式</view>
      <view class="method-list">
        <view class="method-item" :class="{ active: currentMethod === 'location' }" @click="switchMethod('location')">
          <view class="method-title">位置选点</view>
          <view class="method-desc">选择附近地点</view>
        </view>
        <view class="method-item" :class="{ active: currentMethod === 'text' }" @click="switchMethod('text')">
          <view class="method-title">文字录入</view>
          <view class="method-desc">描述行程</view>
        </view>
        <view class="method-item" :class="{ active: currentMethod === 'voice' }" @click="switchMethod('voice')">
          <view class="method-title">语音录入</view>
          <view class="method-desc">语音转文字</view>
        </view>
        <view class="method-item" :class="{ active: currentMethod === 'image' }" @click="switchMethod('image')">
          <view class="method-title">识图录入</view>
          <view class="method-desc">拍摄门头</view>
        </view>
      </view>
    </view>

    <!-- 位置选点 -->
    <view v-if="currentMethod === 'location'" class="section">
      <view class="section-title">选择地点</view>
      <!-- 使用标准地图组件，保持跨平台兼容 -->
      <map 
        id="smartInputMap"
        :latitude="mapCenter.lat" 
        :longitude="mapCenter.lng" 
        :markers="markers" 
        :show-location="true"
        :scale="16"
        style="width: 100%; height: 400rpx;" 
        @markertap="onMarkerTap"
        @callouttap="onMarkerTap"
        @regionchange="onRegionChange"
      />
      <view class="debug-info">
        <text>当前位置: {{ mapCenter.lat }}, {{ mapCenter.lng }}</text>
        <text>地图服务: 跨平台标准地图</text>
      </view>
      <view class="poi-list">
        <view v-if="isLoadingPOI" class="loading-indicator"><i class="fas fa-sync-alt"></i> 正在搜索附近地点...</view>
        <view v-for="(poi, idx) in nearbyPOIs" :key="idx" class="poi-item" @click="selectPOI(poi)">
          <view class="poi-name">{{ poi.name }}</view>
          <view class="poi-address">{{ poi.address }}</view>
          <view class="poi-distance">{{ poi.distance }}m</view>
        </view>
      </view>
    </view>

    <!-- 文字录入 -->
    <view v-if="currentMethod === 'text'" class="section">
      <view class="section-title">描述你的行程</view>
      <textarea 
        class="text-input" 
        v-model="textInput" 
        placeholder="例如：今天下午2点到外滩看黄浦江，然后坐地铁到南京路步行街购物，花了200块钱，体验很棒"
        rows="5"
      />
      <button @click="parseTextInput">解析行程</button>
    </view>

    <!-- 语音录入 -->
    <view v-if="currentMethod === 'voice'" class="section">
      <view class="section-title">语音录入</view>
      <view class="voice-area">
        <button :class="{ recording: isRecording }" @click="toggleRecording">
          {{ isRecording ? '停止录音' : '开始录音' }}
        </button>
        <view v-if="voiceText" class="voice-text">识别结果：{{ voiceText }}</view>
      </view>
    </view>

    <!-- 识图录入 -->
    <view v-if="currentMethod === 'image'" class="section">
      <view class="section-title">拍摄或选择图片</view>
      <view class="image-area">
        <button @click="chooseImage">选择图片</button>
        <button @click="takePhoto">拍照</button>
        <image v-if="selectedImage" :src="selectedImage" class="preview-image" />
        <button v-if="selectedImage" @click="recognizeImage">识别图片</button>
      </view>
    </view>

    <!-- 确认按钮 -->
    <button v-if="selectedPOI || parsedData" class="confirm-btn" @click="confirmInput">确认添加</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 配置将在 onLoad 中从全局获取
      config: null,
      cityId: null,
      pointIndex: null,
      currentMethod: 'location', // location, text, voice, image
      
      // 位置选点
      mapCenter: { lat: 31.2304, lng: 121.4737 }, // 默认上海
      nearbyPOIs: [],
      markers: [],
      selectedPOI: null,
      isLoadingPOI: false, // 新增loading状态
      
      // 文字录入
      textInput: '',
      
      // 语音录入
      isRecording: false,
      
      // 识图录入
      selectedImage: '',
      
      // 解析结果
      parsedData: null
    }
  },
  onLoad(options) {
    // 获取传递的城市和点位索引
    this.cityId = options.cityId
    this.pointIndex = options.pointIndex

    // 从全局获取配置
    try {
      // 尝试多种方式获取全局配置
      this.config = uni.$appConfig ||
                   getApp()?.globalData?.config ||
                   window?.appConfig

      console.log('从全局获取配置:', this.config)
    } catch (e) {
      console.error('获取全局配置失败:', e)
    }

    // 验证配置是否正确加载
    if (!this.config || !this.config.amap || !this.config.amap.key) {
      console.error('配置文件加载失败或配置不完整')
      uni.showModal({
        title: '配置错误',
        content: '配置文件加载失败，请检查 config.js 文件是否正确配置',
        showCancel: false,
        success: () => {
          uni.navigateBack()
        }
      })
      return
    }

    // 调试：检查配置加载状态
    console.log('配置加载成功')
    console.log('高德地图API Key已配置')

    // 获取当前位置
    this.getCurrentLocation()
  },
  methods: {
    switchMethod(method) {
      this.currentMethod = method
      this.resetData()
    },
    resetData() {
      this.selectedPOI = null
      this.parsedData = null
      this.textInput = ''
      this.voiceText = ''
      this.selectedImage = ''
    },
    getCurrentLocation() {
      // 先检查位置权限
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接获取位置
            this.getLocationData()
          } else {
            // 未授权，请求授权
            uni.authorize({
              scope: 'scope.userLocation',
              success: () => {
                this.getLocationData()
              },
              fail: () => {
                uni.showToast({ title: '需要位置权限才能搜索附近地点', icon: 'none' })
                this.searchNearbyPOIs() // 使用默认位置搜索
              }
            })
          }
        },
        fail: () => {
          console.log('获取设置失败，使用默认位置')
          this.searchNearbyPOIs()
        }
      })
    },
    getLocationData() {
      uni.getLocation({
        type: 'gcj02', // 使用国测局坐标系
        success: (res) => {
          console.log('成功获取位置:', res)
          this.mapCenter = { lat: res.latitude, lng: res.longitude }
          this.searchNearbyPOIs()
        },
        fail: (err) => {
          console.error('获取位置失败:', err)
          uni.showToast({ title: '获取位置失败，使用默认位置', icon: 'none' })
          this.searchNearbyPOIs()
        }
      })
    },
    searchNearbyPOIs() {
      console.log('开始搜索POI，当前位置:', this.mapCenter)
      console.log('使用高德地图API Key:', this.config.amap.key)
      this.isLoadingPOI = true

      uni.request({
        url: 'https://restapi.amap.com/v3/place/around',
        method: 'GET',
        data: {
          key: this.config.amap.key,
          location: `${this.mapCenter.lng},${this.mapCenter.lat}`,
          radius: this.config.amap.defaultSearch.radius,
          types: this.config.map.searchTypes,
          offset: this.config.amap.defaultSearch.offset,
          page: this.config.amap.defaultSearch.page,
          extensions: this.config.amap.defaultSearch.extensions
        },
        success: (res) => {
          console.log('高德Web API响应:', res)
          if (res.data.status === '1' && res.data.pois && res.data.pois.length > 0) {
            console.log('成功获取POI数据，共', res.data.pois.length, '条')
            this.nearbyPOIs = res.data.pois.map((poi, index) => ({
              id: index + 1,
              name: poi.name,
              address: poi.address,
              type: poi.type,
              distance: Math.round(parseFloat(poi.distance) || 0),
              lat: parseFloat(poi.location.split(',')[1]),
              lng: parseFloat(poi.location.split(',')[0]),
              tel: poi.tel || '',
              business_area: poi.business_area || ''
            }))
            console.log('处理后的POI数据:', this.nearbyPOIs)
            
            // 更新地图标记
            this.markers = this.nearbyPOIs.slice(0, 10).map(poi => ({
              id: poi.id,
              latitude: poi.lat,
              longitude: poi.lng,
              title: poi.name,
              width: 30,
              height: 30
            }))
            
            uni.showToast({ title: `找到${this.nearbyPOIs.length}个附近地点`, icon: 'success' })
          } else {
            console.log('API返回数据为空或状态异常:', res.data)
            uni.showToast({ title: '附近暂无地点，使用默认数据', icon: 'none' })
            this.loadFallbackPOIs()
          }
        },
        fail: (err) => {
          console.error('高德Web API调用失败:', err)
          uni.showToast({ title: '网络错误，使用默认数据', icon: 'none' })
          this.loadFallbackPOIs()
        },
        complete: () => {
          this.isLoadingPOI = false
        }
      })
    },
    loadFallbackPOIs() {
      // 备用模拟数据
      this.nearbyPOIs = [
        { id: 1, name: '星巴克咖啡', address: '南京东路123号', distance: 50, lat: this.mapCenter.lat + 0.001, lng: this.mapCenter.lng + 0.001, type: '餐饮' },
        { id: 2, name: '便利店', address: '附近商店', distance: 120, lat: this.mapCenter.lat - 0.001, lng: this.mapCenter.lng + 0.001, type: '购物' },
        { id: 3, name: '地铁站', address: '交通枢纽', distance: 200, lat: this.mapCenter.lat + 0.001, lng: this.mapCenter.lng - 0.001, type: '交通' }
      ]
      this.markers = this.nearbyPOIs.map(poi => ({
        id: poi.id,
        latitude: poi.lat,
        longitude: poi.lng,
        title: poi.name
      }))
    },
    onMarkerTap(e) {
      console.log('点击标记:', e)
      const markerId = e.detail.markerId || e.markerId
      const poi = this.nearbyPOIs.find(p => p.id === markerId)
      if (poi) {
        this.selectPOI(poi)
      }
    },
    onRegionChange(e) {
      console.log('地图区域改变:', e)
      // 只在用户拖动结束时搜索，避免频繁请求
      if (e.type === 'end') {
        // 获取地图当前中心点
        const mapContext = uni.createMapContext('smartInputMap', this)
        mapContext.getCenterLocation({
          success: (res) => {
            console.log('获取地图中心点:', res)
            this.mapCenter = {
              lat: res.latitude,
              lng: res.longitude
            }
            // 重新搜索POI
            this.searchNearbyPOIs()
          },
          fail: (err) => {
            console.error('获取地图中心点失败:', err)
          }
        })
      }
    },
    selectPOI(poi) {
      this.selectedPOI = {
        name: poi.name,
        address: poi.address,
        type: poi.type,
        distance: poi.distance,
        lat: poi.lat,
        lng: poi.lng,
        tel: poi.tel
      }
      uni.showToast({ title: `已选择：${poi.name}`, icon: 'success' })
    },
    parseTextInput() {
      if (!this.textInput) {
        uni.showToast({ title: '请输入行程描述', icon: 'none' })
        return
      }
      uni.showLoading({ title: '解析中...' })
      
      // 调用后端API进行大模型解析
      uni.request({
        url: 'https://your-backend-api.com/api/parse-travel-text', // 替换为你的后端API
        method: 'POST',
        data: {
          text: this.textInput
        },
        success: (res) => {
          uni.hideLoading()
          if (res.data.success) {
            this.parsedData = {
              place: res.data.place || '未知地点',
              arriveTime: res.data.arriveTime || '',
              desc: res.data.desc || this.textInput,
              expenses: res.data.expenses || [],
              lat: res.data.lat || null,
              lng: res.data.lng || null
            }
            uni.showToast({ title: '解析成功', icon: 'success' })
          } else {
            uni.showToast({ title: '解析失败，请重试', icon: 'none' })
          }
        },
        fail: (err) => {
          uni.hideLoading()
          console.error('大模型解析失败:', err)
          // 降级到模拟数据
          this.parsedData = {
            place: '解析地点',
            arriveTime: '14:00',
            desc: this.textInput,
            expenses: [{ amount: 0, category: '其他', note: '待完善' }],
            lat: null,
            lng: null
          }
          uni.showToast({ title: '网络错误，使用模拟解析', icon: 'none' })
        }
      })
    },
    toggleRecording() {
      this.isRecording = !this.isRecording
      if (this.isRecording) {
        // 开始录音，实际开发中使用语音识别API
        uni.showToast({ title: '开始录音', icon: 'none' })
      } else {
        // 停止录音并识别
        uni.showLoading({ title: '识别中...' })
        setTimeout(() => {
          uni.hideLoading()
          this.voiceText = '今天下午去了外滩，风景很好'
          this.textInput = this.voiceText
          this.parseTextInput()
        }, 1500)
      }
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0]
        }
      })
    },
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0]
        }
      })
    },
    recognizeImage() {
      if (!this.selectedImage) return
      uni.showLoading({ title: '识别中...' })
      
      // 调用后端OCR+大模型识别API
      uni.uploadFile({
        url: 'https://your-backend-api.com/api/recognize-image', // 替换为你的后端API
        filePath: this.selectedImage,
        name: 'image',
        success: (res) => {
          uni.hideLoading()
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              this.selectedPOI = {
                name: data.name || '识别的店名',
                address: data.address || '识别的地址',
                type: data.type || '其他',
                lat: data.lat || null,
                lng: data.lng || null
              }
              uni.showToast({ title: '识别成功', icon: 'success' })
            } else {
              uni.showToast({ title: '识别失败，请重试', icon: 'none' })
            }
          } catch (e) {
            console.error('解析识别结果失败:', e)
            uni.showToast({ title: '识别结果解析失败', icon: 'none' })
          }
        },
        fail: (err) => {
          uni.hideLoading()
          console.error('图像识别失败:', err)
          // 降级到模拟数据
          this.selectedPOI = {
            name: '识别的店名',
            address: '识别的地址',
            type: '餐饮',
            lat: null,
            lng: null
          }
          uni.showToast({ title: '网络错误，使用模拟识别', icon: 'none' })
        }
      })
    },
    confirmInput() {
      const data = this.selectedPOI || this.parsedData
      if (!data) {
        uni.showToast({ title: '请先选择或录入信息', icon: 'none' })
        return
      }
      
      // 统一数据格式，确保包含经纬度
      const pointData = {
        place: data.name || data.place,
        arriveTime: data.arriveTime || '',
        desc: data.desc || data.address || '',
        expenses: data.expenses || [],
        lat: data.lat || null, // 添加纬度
        lng: data.lng || null  // 添加经度
      }
      
      // 返回到记录页面并填充数据
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      if (prevPage && prevPage.$vm.fillPointData) {
        prevPage.$vm.fillPointData(this.cityId, this.pointIndex, pointData)
      }
      uni.navigateBack()
    }
  }
}
</script>

<style>
.smart-container { padding: 32rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx; }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #05D592; }
.method-list { display: flex; flex-wrap: wrap; }
.method-item { width: 50%; padding: 12rpx; box-sizing: border-box; border: 2rpx solid #f9f9f9; border-radius: 12rpx; margin-bottom: 12rpx; background: #f9f9f9; }
.method-item.active { border-color: #05D592; background: #EDF7F3; }
.method-title { font-size: 28rpx; font-weight: bold; }
.method-desc { font-size: 22rpx; color: #888; margin-top: 4rpx; }
.poi-list { margin-top: 16rpx; max-height: 300rpx; overflow-y: auto; }
.poi-item { padding: 16rpx; border-bottom: 1rpx solid #EDF7F3; }
.poi-name { font-size: 28rpx; font-weight: bold; }
.poi-address { font-size: 24rpx; color: #666; margin: 4rpx 0; }
.poi-distance { font-size: 22rpx; color: #05D592; }
.text-input { width: 100%; min-height: 120rpx; border: 1rpx solid #EDF7F3; border-radius: 8rpx; padding: 12rpx; font-size: 28rpx; background: #fff; }
.voice-area { text-align: center; }
.recording { background: #ff4757 !important; color: #fff; }
.voice-text { margin-top: 16rpx; font-size: 26rpx; }
.image-area { text-align: center; }
.preview-image { width: 200rpx; height: 200rpx; margin: 16rpx; border-radius: 8rpx; }
.confirm-btn { margin-top: 32rpx; width: 100%; background: #05D592; color: #fff; font-size: 32rpx; border-radius: 32rpx; }
.debug-info { margin-top: 16rpx; padding: 12rpx; background: #f0f0f0; border-radius: 8rpx; font-size: 24rpx; color: #333; }
.loading-indicator { text-align: center; padding: 20rpx; font-size: 28rpx; color: #FF6B35; }
.loading-indicator i { margin-right: 8rpx; animation: spin 1s linear infinite; }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
</style> 
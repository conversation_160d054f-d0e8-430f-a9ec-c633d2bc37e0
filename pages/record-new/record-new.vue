<template>
  <view class="record-container">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="header-title">{{ isEditMode ? '编辑旅行' : '新建旅行' }}</view>
      <view class="header-subtitle">{{ getTravelSummary() }}</view>
    </view>

    <!-- 垂直时间线内容 -->
    <view class="timeline-content">
      <!-- 时间线列表 -->
      <view class="timeline-list">
        <view
          v-for="(day, index) in travelDays"
          :key="index"
          class="timeline-day"
        >
          <!-- Day标题 -->
          <view class="day-header" @click="toggleDateDisplay">
            <view class="day-title-wrapper">
              <view class="day-indicator">
                <view class="day-line" v-if="index > 0"></view>
                <view class="day-dot"></view>
                <view class="day-line" v-if="index < travelDays.length - 1"></view>
              </view>
              <view class="day-title-content">
                <view class="day-title">Day {{ index + 1 }}</view>
                <view class="day-subtitle">{{ getDaySubtitle(day) }}</view>
              </view>
              <!-- 日期显示区域 -->
              <view class="day-date-area" v-if="showAllDates">
                <picker mode="date" :value="day.date" @change="e => setDayDate(index, e)" @click.stop>
                  <view class="day-date-display" :class="{ 'no-date': !day.date }">
                    {{ day.date || '选择日期' }}
                  </view>
                </picker>
              </view>
            </view>
            <view class="day-actions">
              <button class="day-action-btn" @click.stop="removeDay(index)" v-if="travelDays.length > 1">
                <i class="fas fa-trash"></i>
              </button>
            </view>
          </view>

          <!-- Day内容区域 -->
          <view class="day-content">
            <!-- 当日统计 - 只有在有点位时才显示 -->
            <view class="day-stats" v-if="day.points.length > 0">
              <view class="stat-item">
                <view class="stat-number">{{ day.points.length }}</view>
                <view class="stat-label">点位</view>
              </view>
              <view class="stat-item">
                <view class="stat-number">{{ getDayExpenseTotal(day) }}</view>
                <view class="stat-label">消费</view>
              </view>
              <view class="stat-item">
                <view class="stat-number">{{ getDayAverageExperience(day) }}</view>
                <view class="stat-label">体验</view>
              </view>
            </view>

            <!-- 点位列表或添加区域 -->
            <view class="points-area">
              <view v-if="day.points.length > 0" class="points-list">
                <view
                  v-for="(point, pointIndex) in day.points"
                  :key="pointIndex"
                  class="point-item"
                  @click="editPoint(index, pointIndex)"
                >
                  <view class="point-content">
                    <view class="point-header">
                      <view class="point-number">{{ pointIndex + 1 }}</view>
                      <view class="point-info">
                        <view class="point-name">{{ point.place || '未命名地点' }}</view>
                        <view class="point-meta">
                          <text v-if="point.arriveTime" class="point-time">{{ point.arriveTime }}</text>
                          <text v-if="point.duration" class="point-duration">{{ point.duration }}</text>
                        </view>
                      </view>
                      <button class="point-delete" @click.stop="removePoint(index, pointIndex)">
                        <i class="fas fa-times"></i>
                      </button>
                    </view>

                    <view class="point-preview" v-if="point.desc">
                      <text class="point-desc">{{ point.desc }}</text>
                    </view>

                    <view class="point-extras">
                      <view class="point-media" v-if="point.media.length > 0">
                        <i class="fas fa-camera"></i>
                        <text>{{ point.media.length }}张照片</text>
                      </view>
                      <view class="point-expense" v-if="getPointExpenseTotal(point) > 0">
                        <i class="fas fa-yen-sign"></i>
                        <text>{{ getPointExpenseTotal(point) }}元</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 添加点位区域 -->
              <view class="add-point-area" @click="addPoint(index)">
                <view class="add-point-content">
                  <i class="fas fa-plus"></i>
                  <text>{{ day.points.length > 0 ? '继续添加点位' : '添加第 ' + (index + 1) + ' 天的安排' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加新一天 -->
      <view class="add-day-section">
        <button class="add-day-btn" @click="addDay">
          <i class="fas fa-plus"></i>
          <text>添加新的一天</text>
        </button>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" @click="previewTravel">
        <i class="fas fa-eye"></i>
        <text>预览</text>
      </button>
      <button class="action-btn primary" @click="saveTravel">
        <i class="fas fa-save"></i>
        <text>保存</text>
      </button>
    </view>

    <!-- 城市编辑模态框 -->
    <view class="modal-overlay" v-if="showCityModal" @click="closeCityModal">
      <view class="modal-content city-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ editingCityIndex === -1 ? '添加城市' : '编辑城市' }}</view>
          <button class="close-btn" @click="closeCityModal">
            <i class="fas fa-times"></i>
          </button>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <label class="form-label">城市名称</label>
            <input 
              class="form-input" 
              v-model="currentCity.city" 
              placeholder="请输入城市名称"
              @input="onCityNameChange"
            />
          </view>
          
          <!-- 城市点位列表 -->
          <view class="city-points">
            <view class="points-header">
              <text class="points-title">旅行点位</text>
              <button class="add-point-btn" @click="addPoint">
                <i class="fas fa-plus"></i>
                <text>添加点位</text>
              </button>
            </view>
            
            <view class="points-list">
              <view 
                v-for="(point, index) in currentCity.points" 
                :key="index"
                class="point-item"
                @click="editPoint(editingCityIndex, index)"
              >
                <view class="point-number">{{ index + 1 }}</view>
                <view class="point-content">
                  <view class="point-name">{{ point.place || '未命名地点' }}</view>
                  <view class="point-time" v-if="point.arriveTime">{{ point.arriveTime }}</view>
                </view>
                <view class="point-actions">
                  <button class="action-icon" @click.stop="removePoint(index)">
                    <i class="fas fa-trash"></i>
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeCityModal">取消</button>
          <button class="modal-btn primary" @click="saveCityChanges">保存</button>
        </view>
      </view>
    </view>

    <!-- 点位编辑模态框 -->
    <view class="modal-overlay" v-if="showPointModal" @click="closePointModal">
      <view class="modal-content point-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">编辑点位</view>
          <button class="close-btn" @click="closePointModal">
            <i class="fas fa-times"></i>
          </button>
        </view>

        <view class="modal-body">
          <!-- 基本信息 -->
          <view class="form-section">
            <view class="section-header">基本信息</view>

            <view class="form-group">
              <label class="form-label">地点名称</label>
              <view class="input-with-action">
                <input
                  class="form-input"
                  v-model="currentPoint.place"
                  placeholder="请输入地点名称"
                  @input="autoSavePoint"
                />
                <button class="input-action-btn" @click="getLocation">
                  <i class="fas fa-map-marker-alt"></i>
                </button>
                <button class="input-action-btn map-btn" @click="openMapPicker">
                  <i class="fas fa-map"></i>
                </button>
                <button class="input-action-btn smart-btn" @click="openSmartInput">
                  <i class="fas fa-robot"></i>
                </button>
              </view>
            </view>



            <view class="form-row">
              <view class="form-group half">
                <label class="form-label">到达时间</label>
                <picker
                  mode="time"
                  :value="currentPoint.arriveTime"
                  @change="setArriveTime"
                >
                  <view class="picker-display">
                    {{ currentPoint.arriveTime || '选择时间' }}
                  </view>
                </picker>
              </view>

              <view class="form-group half">
                <label class="form-label">停留时长</label>
                <input
                  class="form-input"
                  v-model="currentPoint.duration"
                  placeholder="如：2小时"
                  @input="autoSavePoint"
                />
              </view>
            </view>

            <view class="form-group">
              <label class="form-label">描述</label>
              <textarea
                class="form-textarea"
                v-model="currentPoint.desc"
                placeholder="记录这个地方的感受..."
                maxlength="200"
                @input="autoSavePoint"
              />
            </view>
          </view>

          <!-- 体验评分 -->
          <view class="form-section">
            <view class="section-header">体验评分</view>
            <view class="experience-slider">
              <slider
                min="-5"
                max="5"
                step="1"
                :value="currentPoint.experience"
                @change="setExperience"
                activeColor="#05D592"
                backgroundColor="#EDF7F3"
                block-color="#05D592"
                show-value
              />
              <view class="slider-labels">
                <text class="label-left">很差</text>
                <text class="label-right">很棒</text>
              </view>
            </view>
          </view>

          <!-- 媒体文件 -->
          <view class="form-section">
            <view class="section-header">
              <text>照片</text>
              <button class="section-action" @click="chooseMedia">
                <i class="fas fa-camera"></i>
                <text>添加照片</text>
              </button>
            </view>

            <view class="media-grid" v-if="currentPoint.media.length > 0">
              <view
                v-for="(img, index) in currentPoint.media"
                :key="index"
                class="media-item"
              >
                <image :src="img" class="media-image" mode="aspectFill" />
                <button class="media-remove" @click="removeMedia(index)">
                  <i class="fas fa-times"></i>
                </button>
              </view>
            </view>
          </view>

          <!-- 消费记录 -->
          <view class="form-section">
            <view class="section-header">
              <text>消费记录</text>
              <button class="section-action" @click="addExpense">
                <i class="fas fa-plus"></i>
                <text>添加消费</text>
              </button>
            </view>

            <view class="expenses-list">
              <view
                v-for="(expense, index) in currentPoint.expenses"
                :key="index"
                class="expense-item"
              >
                <view class="expense-row">
                  <input
                    class="expense-amount"
                    v-model="expense.amount"
                    type="number"
                    placeholder="金额"
                    @input="autoSavePoint"
                  />
                  <picker
                    :range="expenseCategories"
                    @change="e => setExpenseCategory(index, e.detail.value)"
                  >
                    <view class="expense-category">
                      {{ expense.category || '选择类别' }}
                    </view>
                  </picker>
                  <input
                    class="expense-note"
                    v-model="expense.note"
                    placeholder="备注"
                    @input="autoSavePoint"
                  />
                  <button class="expense-remove" @click="removeExpense(index)">
                    <i class="fas fa-trash"></i>
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closePointModal">取消</button>
          <button class="modal-btn primary" @click="savePointChanges">保存</button>
        </view>
      </view>
    </view>

    <!-- AI智能录入模态框 -->
    <view class="modal-overlay" v-if="showSmartModal" @click="closeSmartModal">
      <view class="modal-content ai-smart-modal" @click.stop>
        <view class="modal-header">
          <view class="ai-header">
            <i class="fas fa-robot ai-icon"></i>
            <text class="modal-title">AI智能录入</text>
          </view>
          <button class="close-btn" @click="closeSmartModal">
            <i class="fas fa-times"></i>
          </button>
        </view>

        <view class="modal-body">
          <view class="ai-intro">
            <text class="intro-text">描述你的旅行经历，AI将自动为你创建完整的旅行记录</text>
          </view>

          <!-- 输入方式选择 -->
          <view class="input-method-tabs">
            <button
              class="method-tab"
              :class="{ active: inputMethod === 'text' }"
              @click="inputMethod = 'text'"
            >
              <i class="fas fa-keyboard"></i>
              <text>文字输入</text>
            </button>
            <button
              class="method-tab"
              :class="{ active: inputMethod === 'voice' }"
              @click="inputMethod = 'voice'"
            >
              <i class="fas fa-microphone"></i>
              <text>语音输入</text>
            </button>
          </view>

          <!-- 文字输入 -->
          <view v-if="inputMethod === 'text'" class="input-section">
            <textarea
              class="ai-text-input"
              v-model="aiInput"
              placeholder="例如：今天上午10点从酒店出发，先去了外滩看黄浦江，风景很美，拍了很多照片。中午在南京路步行街吃了小笼包，花了80块钱。下午去了豫园逛街，买了一些纪念品，总共花了200块。晚上在外滩看夜景，然后回酒店休息。"
              :maxlength="1000"
            />
            <view class="input-counter">{{ aiInput.length }}/1000</view>
          </view>

          <!-- 语音输入 -->
          <view v-if="inputMethod === 'voice'" class="input-section">
            <view class="voice-input-area">
              <button class="ai-voice-btn" :class="{ recording: isRecording }" @click="toggleRecording">
                <i class="fas" :class="isRecording ? 'fa-stop' : 'fa-microphone'"></i>
                <text>{{ isRecording ? '点击停止录音' : '点击开始录音' }}</text>
              </button>
              <view v-if="voiceText" class="voice-result">
                <text class="voice-label">识别结果：</text>
                <text class="voice-content">{{ voiceText }}</text>
                <button class="use-voice-btn" @click="useVoiceResult">使用此结果</button>
              </view>
            </view>
          </view>

          <!-- AI解析结果 -->
          <view v-if="aiResult" class="ai-result-section">
            <view class="result-header">
              <i class="fas fa-magic result-icon"></i>
              <text class="result-title">AI解析结果</text>
            </view>

            <view class="result-preview">
              <view class="preview-item" v-if="aiResult.title">
                <text class="preview-label">行程标题：</text>
                <text class="preview-value">{{ aiResult.title }}</text>
              </view>

              <view class="preview-item" v-if="aiResult.days && aiResult.days.length > 0">
                <text class="preview-label">识别天数：</text>
                <text class="preview-value">{{ aiResult.days.length }}天</text>
              </view>

              <view class="preview-item" v-if="aiResult.totalPoints">
                <text class="preview-label">识别点位：</text>
                <text class="preview-value">{{ aiResult.totalPoints }}个</text>
              </view>

              <view class="preview-item" v-if="aiResult.totalExpense">
                <text class="preview-label">总消费：</text>
                <text class="preview-value">¥{{ aiResult.totalExpense }}</text>
              </view>
            </view>

            <view class="result-details">
              <view v-for="(day, dayIndex) in aiResult.days" :key="dayIndex" class="day-preview">
                <text class="day-preview-title">第{{ dayIndex + 1 }}天 ({{ day.date }})</text>
                <view class="points-preview">
                  <view v-for="(point, pointIndex) in day.points" :key="pointIndex" class="point-preview">
                    <text class="point-name">{{ point.place }}</text>
                    <text class="point-time" v-if="point.arriveTime">{{ point.arriveTime }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeSmartModal">取消</button>
          <button
            class="modal-btn ai-parse"
            @click="parseWithAI"
            :disabled="!aiInput.trim() && !voiceText"
            v-if="!aiResult"
          >
            <i class="fas fa-robot"></i>
            <text>AI解析</text>
          </button>
          <button class="modal-btn primary" @click="confirmAIResult" v-if="aiResult">
            <i class="fas fa-check"></i>
            <text>应用结果</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 地图选点模态框 -->
    <view class="modal-overlay" v-if="showMapModal" @click="closeMapModal">
      <view class="modal-content map-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">地图选点</text>
          <button class="close-btn" @click="closeMapModal">
            <i class="fas fa-times"></i>
          </button>
        </view>

        <view class="modal-body">
          <!-- 地图容器 -->
          <view class="map-container">
            <map
              id="smartInputMap"
              class="smart-map"
              :latitude="mapCenter.lat"
              :longitude="mapCenter.lng"
              :scale="mapScale"
              :markers="mapMarkers"
              :show-location="true"
              :enable-3D="false"
              :enable-overlooking="false"
              :enable-satellite="false"
              :enable-traffic="false"
              @tap="onMapTap"
              @markertap="onMarkerTap"
              @regionchange="onRegionChange"
            ></map>

            <!-- 地图上的搜索框 -->
            <view class="map-search-overlay">
              <view class="search-bar">
                <input
                  class="search-input"
                  v-model="poiKeyword"
                  placeholder="搜索地点..."
                  @confirm="searchPOI"
                />
                <button class="search-btn" @click="searchPOI">
                  <i class="fas fa-search"></i>
                </button>
              </view>

              <!-- 当前位置按钮 -->
              <button class="current-location-btn" @click="moveToCurrentLocation">
                <i class="fas fa-crosshairs"></i>
              </button>
            </view>
          </view>

          <!-- POI列表 -->
          <view class="poi-list-section">
            <view class="poi-list-header">
              <text class="list-title">附近地点</text>
              <button class="refresh-btn" @click="refreshPOI" :disabled="isLoadingPOI">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoadingPOI }"></i>
              </button>
            </view>

            <view v-if="isLoadingPOI" class="loading-indicator">
              <i class="fas fa-spinner fa-spin"></i>
              <text>搜索中...</text>
            </view>

            <view v-else-if="poiList.length === 0 && hasSearched" class="empty-poi">
              <text>未找到相关地点</text>
            </view>

            <view v-else class="poi-list">
              <view
                v-for="(poi, index) in poiList"
                :key="poi.id || index"
                class="poi-item"
                :class="{ selected: selectedPOI && selectedPOI.id === poi.id }"
                @click="selectPOI(poi)"
              >
                <view class="poi-info">
                  <text class="poi-name">{{ poi.name }}</text>
                  <text class="poi-address">{{ poi.address || poi.adname }}</text>
                  <view class="poi-tags" v-if="poi.type">
                    <text class="poi-tag">{{ getSimplifiedType(poi.type) }}</text>
                  </view>
                </view>
                <view class="poi-distance" v-if="poi.distance">
                  <text>{{ formatDistance(poi.distance) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeMapModal">取消</button>
          <button
            class="modal-btn primary"
            @click="confirmPOISelection"
            :disabled="!selectedPOI"
          >
            确认选择
          </button>
        </view>
      </view>
    </view>

    <!-- 自定义底部导航栏 -->
    <custom-tabbar :current="1"></custom-tabbar>
  </view>
</template>

<script>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

// 内嵌配置，避免导入问题
const config = {
  amap: {
    key: '3ffb35a19f3dd49a1026aba990fffb35',
    baseUrl: 'https://restapi.amap.com',
    defaultSearch: {
      radius: 1000,
      offset: 20,
      page: 1,
      extensions: 'base'
    }
  },
  map: {
    defaultCenter: {
      lat: 31.2304,
      lng: 121.4737
    },
    defaultScale: 16
  },
  dev: {
    debug: true,
    useMockData: false
  }
}

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      // 配置文件 - 直接使用默认配置，避免导入问题
      config: {
        amap: {
          key: '3ffb35a19f3dd49a1026aba990fffb35',
          baseUrl: 'https://restapi.amap.com',
          defaultSearch: {
            radius: 1000,
            offset: 20,
            page: 1,
            extensions: 'base'
          }
        },
        map: {
          defaultCenter: {
            lat: 31.2304,
            lng: 121.4737
          },
          defaultScale: 16
        },
        dev: {
          debug: true,
          useMockData: false
        }
      },
      recordId: null,
      isEditMode: false,
      // 新的数据结构：以天为单位
      travelDays: [
        {
          date: '',
          city: '',
          points: []
        }
      ],
      expenseCategories: ['餐饮', '住宿', '市内交通', '大交通', '纪念品', '门票', '其他'],

      // 日期显示状态
      showAllDates: true, // 控制是否显示所有Day的日期，新建时默认显示

      // 模态框状态
      showPointModal: false,
      showCityModal: false,
      editingDayIndex: -1,
      editingPointIndex: -1,
      editingCityIndex: -1,

      // 当前编辑的数据
      currentPoint: {},
      currentCity: {
        city: '',
        points: []
      },

      // AI智能录入相关
      showSmartModal: false,
      inputMethod: 'text', // text, voice
      aiInput: '', // AI输入内容
      isRecording: false,
      voiceText: '',
      aiResult: null, // AI解析结果
      isProcessing: false,

      // 地图选点相关
      showMapModal: false,
      poiKeyword: '',
      poiList: [],
      selectedPOI: null,
      isLoadingPOI: false,
      hasSearched: false,

      // 地图状态
      mapCenter: {
        lat: 31.2304, // 默认上海
        lng: 121.4737
      },
      mapScale: 16,
      mapMarkers: [],
      mapContext: null
    }
  },

  onLoad(options) {
    // 确保配置正确加载
    this.ensureConfigLoaded()

    if (options.id) {
      this.recordId = options.id
      this.isEditMode = true
      this.loadRecord(options.id)
      // 编辑模式时，根据数据决定是否显示日期
      this.showAllDates = false
    } else {
      // 新建模式，默认显示日期选择，并设置第一天为今天
      this.showAllDates = true
      this.travelDays[0].date = new Date().toISOString().split('T')[0]
    }
  },

  created() {
    // 组件创建时确保配置加载
    this.ensureConfigLoaded()
  },

  methods: {
    // 确保配置正确加载
    ensureConfigLoaded() {
      // 使用内嵌配置，确保配置始终可用
      if (!this.config || !this.config.amap) {
        this.config = config
      }

      // 验证配置完整性
      if (!this.config || !this.config.amap || !this.config.amap.key) {
        console.error('配置验证失败，配置可能不完整')
        return false
      }

      console.log('配置加载完成:', this.config.amap.key ? '有效' : '无效')
      return true
    },

    // 数据加载和转换
    loadRecord(id) {
      const records = uni.getStorageSync('records') || []
      const record = records.find(r => String(r.id) === String(id))
      if (record) {
        if (record.travelDays) {
          // 新格式数据
          this.travelDays = record.travelDays
        } else if (record.travel) {
          // 兼容旧格式数据，转换为新格式
          this.convertOldFormatToNew(record.travel)
        }
      }
    },

    // 将旧格式（按城市）转换为新格式（按天）
    convertOldFormatToNew(oldTravel) {
      this.travelDays = []
      let dayIndex = 0

      oldTravel.forEach(city => {
        if (city.points && city.points.length > 0) {
          city.points.forEach(point => {
            if (this.travelDays[dayIndex] && this.travelDays[dayIndex].points.length >= 5) {
              // 如果当前天的点位太多，创建新的一天
              dayIndex++
            }

            if (!this.travelDays[dayIndex]) {
              this.travelDays[dayIndex] = {
                date: '',
                city: city.city || '',
                points: []
              }
            }

            this.travelDays[dayIndex].points.push(point)
          })
        }
      })

      if (this.travelDays.length === 0) {
        this.travelDays = [{
          date: '',
          city: '',
          points: []
        }]
      }
    },

    // 获取日期副标题
    getDaySubtitle() {
      // 不显示任何副标题内容
      return ''
    },

    // 切换日期显示/隐藏
    toggleDateDisplay() {
      this.showAllDates = !this.showAllDates
    },

    // 检查是否应该显示日期选择器
    shouldShowDatePicker() {
      // 如果用户手动切换了显示状态，则遵循用户选择
      if (this.showAllDates !== null) {
        return this.showAllDates
      }

      // 如果是新建模式或者有空日期，则显示
      return !this.isEditMode || this.travelDays.some(day => !day.date)
    },



    // 统计方法
    getTravelSummary() {
      if (this.travelDays.length === 0) return '开始规划你的旅行'
      const cities = [...new Set(this.travelDays.filter(d => d.city).map(d => d.city))]
      const duration = this.travelDays.length
      return cities.length > 0 ? `${cities.join(' → ')} · ${duration}天` : `${duration}天旅行`
    },

    getTotalPoints() {
      return this.travelDays.reduce((total, day) => total + day.points.length, 0)
    },

    getTotalExpense() {
      let total = 0
      this.travelDays.forEach(day => {
        day.points.forEach(point => {
          point.expenses.forEach(expense => {
            if (expense.amount) {
              total += Number(expense.amount) || 0
            }
          })
        })
      })
      return total.toFixed(0)
    },

    getAverageExperience() {
      let total = 0
      let count = 0
      this.travelDays.forEach(day => {
        day.points.forEach(point => {
          if (point.experience !== undefined) {
            total += point.experience
            count++
          }
        })
      })
      return count > 0 ? (total / count).toFixed(1) : '0'
    },

    // 单日统计方法
    getDayExpenseTotal(day) {
      let total = 0
      day.points.forEach(point => {
        point.expenses.forEach(expense => {
          if (expense.amount) {
            total += Number(expense.amount) || 0
          }
        })
      })
      return total.toFixed(0)
    },

    getDayAverageExperience(day) {
      let total = 0
      let count = 0
      day.points.forEach(point => {
        if (point.experience !== undefined) {
          total += point.experience
          count++
        }
      })
      return count > 0 ? (total / count).toFixed(1) : '0'
    },

    getPointExpenseTotal(point) {
      let total = 0
      point.expenses.forEach(expense => {
        if (expense.amount) {
          total += Number(expense.amount) || 0
        }
      })
      return total.toFixed(0)
    },

    // 天操作
    addDay() {
      const newDay = {
        date: '',
        city: '',
        points: []
      }
      this.travelDays.push(newDay)
      // 移除toast提示，避免冗余信息
    },

    removeDay(index) {
      if (this.travelDays.length <= 1) {
        uni.showToast({ title: '至少需要保留一天', icon: 'none' })
        return
      }

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这一天的行程吗？',
        success: (res) => {
          if (res.confirm) {
            this.travelDays.splice(index, 1)
            uni.showToast({ title: '删除成功', icon: 'success' })
          }
        }
      })
    },

    setDayDate(dayIndex, e) {
      this.travelDays[dayIndex].date = e.detail.value
    },

    // 点位操作
    addPoint(dayIndex) {
      // 创建新点位但不立即添加到数组中
      const newPoint = {
        place: '',
        arriveTime: '',
        duration: '',
        desc: '',
        media: [],
        experience: 0,
        expenses: [],
        lat: null,
        lng: null
      }

      // 设置为新建模式
      this.editingDayIndex = dayIndex
      this.editingPointIndex = -1 // -1 表示新建
      this.currentPoint = JSON.parse(JSON.stringify(newPoint))
      this.showPointModal = true
    },

    removePoint(dayIndex, pointIndex) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个点位吗？',
        success: (res) => {
          if (res.confirm) {
            this.travelDays[dayIndex].points.splice(pointIndex, 1)
            uni.showToast({ title: '删除成功', icon: 'success' })
          }
        }
      })
    },

    editPoint(dayIndex, pointIndex) {
      this.editingDayIndex = dayIndex
      this.editingPointIndex = pointIndex
      this.currentPoint = JSON.parse(JSON.stringify(this.travelDays[dayIndex].points[pointIndex]))
      this.showPointModal = true
    },

    closePointModal() {
      // 在关闭模态框前，如果是编辑现有点位，自动保存当前更改
      if (this.editingDayIndex >= 0 && this.editingPointIndex >= 0) {
        this.travelDays[this.editingDayIndex].points[this.editingPointIndex] = JSON.parse(JSON.stringify(this.currentPoint))
      }

      this.showPointModal = false
      this.editingDayIndex = -1
      this.editingPointIndex = -1
      this.currentPoint = {}
    },

    savePointChanges() {
      if (this.editingDayIndex >= 0) {
        if (this.editingPointIndex >= 0) {
          // 编辑现有点位
          this.travelDays[this.editingDayIndex].points[this.editingPointIndex] = JSON.parse(JSON.stringify(this.currentPoint))
        } else {
          // 新建点位
          this.travelDays[this.editingDayIndex].points.push(JSON.parse(JSON.stringify(this.currentPoint)))
        }
        // 移除错误的 this.autoSave() 调用，因为该方法不存在
        this.closePointModal()
        uni.showToast({ title: '保存成功', icon: 'success' })
      }
    },

    // 自动保存点位数据
    autoSavePoint() {
      if (this.editingDayIndex >= 0 && this.editingPointIndex >= 0) {
        this.travelDays[this.editingDayIndex].points[this.editingPointIndex] = JSON.parse(JSON.stringify(this.currentPoint))
      }
    },

    // 点位详细操作
    getLocation() {
      uni.showLoading({ title: '获取位置中...' })

      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 使用逆地理编码获取地点名称
          this.reverseGeocode(res.latitude, res.longitude)
        },
        fail: () => {
          uni.hideLoading()
          uni.showToast({ title: '定位失败', icon: 'none' })
        }
      })
    },

    // 逆地理编码获取地点名称
    reverseGeocode(lat, lng) {
      // 模拟逆地理编码API调用，实际开发中应该调用真实的地图API
      setTimeout(() => {
        uni.hideLoading()

        // 模拟返回的地点信息
        const mockPlaces = [
          '星巴克咖啡(南京东路店)',
          '上海外滩观景台',
          '南京路步行街',
          '人民广场地铁站',
          '上海博物馆',
          '豫园商城',
          '陆家嘴金融中心',
          '城隍庙小吃街'
        ]

        const randomPlace = mockPlaces[Math.floor(Math.random() * mockPlaces.length)]

        // 填充地点名称，同时保存经纬度信息
        this.currentPoint.place = randomPlace
        this.currentPoint.lat = lat
        this.currentPoint.lng = lng

        this.autoSavePoint()
        uni.showToast({ title: '位置获取成功', icon: 'success' })
      }, 1500)
    },

    setArriveTime(e) {
      this.currentPoint.arriveTime = e.detail.value
      this.autoSavePoint()
    },

    setExperience(e) {
      this.currentPoint.experience = e.detail.value
      this.autoSavePoint()
    },

    chooseMedia() {
      uni.chooseImage({
        count: 3,
        success: (res) => {
          this.currentPoint.media = this.currentPoint.media.concat(res.tempFilePaths)
          this.autoSavePoint()
        }
      })
    },

    removeMedia(index) {
      this.currentPoint.media.splice(index, 1)
      this.autoSavePoint()
    },

    // 消费操作
    addExpense() {
      this.currentPoint.expenses.push({
        amount: '',
        category: '',
        note: ''
      })
      this.autoSavePoint()
    },

    removeExpense(index) {
      this.currentPoint.expenses.splice(index, 1)
      this.autoSavePoint()
    },

    setExpenseCategory(index, categoryIndex) {
      this.currentPoint.expenses[index].category = this.expenseCategories[categoryIndex]
      this.autoSavePoint()
    },

    // 主要操作
    previewTravel() {
      if (this.isEditMode && this.recordId) {
        // 编辑模式下，跳转到详情页面预览
        uni.navigateTo({ url: `/pages/detail/detail?id=${this.recordId}` })
      } else {
        // 新建模式下，需要先保存才能预览
        uni.showModal({
          title: '提示',
          content: '需要先保存记录才能预览，是否立即保存？',
          success: (res) => {
            if (res.confirm) {
              this.saveTravel()
            }
          }
        })
      }
    },

    saveTravel() {
      // 校验
      if (!this.travelDays.length) {
        uni.showToast({ title: '请至少添加一天行程', icon: 'none' })
        return
      }

      // 检查是否有空的日期
      const hasEmptyDates = this.travelDays.some(day => !day.date)
      if (hasEmptyDates) {
        uni.showToast({ title: '请为所有日期设置具体时间', icon: 'none' })
        return
      }

      let records = uni.getStorageSync('records') || []

      // 生成标题和地点信息（从day中提取城市信息）
      const cities = [...new Set(this.travelDays.filter(d => d.city).map(d => d.city))]
      const startDate = this.travelDays[0].date
      const endDate = this.travelDays[this.travelDays.length - 1].date

      if (this.isEditMode && this.recordId) {
        // 编辑模式
        const idx = records.findIndex(r => String(r.id) === String(this.recordId))
        if (idx !== -1) {
          records[idx].travelDays = this.travelDays
          records[idx].title = this.getTravelSummary()
          records[idx].place = cities.join('、') || '未知地点'
          records[idx].date = startDate
          records[idx].endDate = endDate
          // 保持向后兼容，同时保存旧格式
          records[idx].travel = this.convertNewFormatToOld()
        }
      } else {
        // 新建模式
        const newId = Date.now()
        records.push({
          id: newId,
          title: this.getTravelSummary(),
          desc: '',
          date: startDate,
          endDate: endDate,
          place: cities.join('、') || '未知地点',
          travelDays: JSON.parse(JSON.stringify(this.travelDays)),
          // 保持向后兼容，同时保存旧格式
          travel: this.convertNewFormatToOld()
        })
      }

      uni.setStorageSync('records', records)
      uni.showToast({ title: '保存成功', icon: 'success' })

      setTimeout(() => {
        uni.switchTab ? uni.switchTab({ url: '/pages/index/index' }) : uni.reLaunch({ url: '/pages/index/index' })
      }, 500)
    },

    // 将新格式转换为旧格式以保持兼容性
    convertNewFormatToOld() {
      const cityMap = new Map()

      this.travelDays.forEach(day => {
        if (day.city) {
          if (!cityMap.has(day.city)) {
            cityMap.set(day.city, {
              city: day.city,
              cityInfo: {},
              points: []
            })
          }
          cityMap.get(day.city).points.push(...day.points)
        }
      })

      return Array.from(cityMap.values())
    },

    // AI智能录入相关方法
    openSmartInput() {
      this.showSmartModal = true
      this.resetAIData()
    },

    closeSmartModal() {
      this.showSmartModal = false
      this.resetAIData()
    },

    resetAIData() {
      this.aiInput = ''
      this.voiceText = ''
      this.aiResult = null
      this.isRecording = false
      this.isProcessing = false
      this.inputMethod = 'text'
    },

    // AI解析核心方法
    parseWithAI() {
      const inputText = this.aiInput.trim() || this.voiceText.trim()

      if (!inputText) {
        uni.showToast({ title: '请输入旅行描述', icon: 'none' })
        return
      }

      this.isProcessing = true
      uni.showLoading({ title: 'AI解析中...' })

      // 模拟AI大模型解析，实际开发中应该调用真实的AI API
      setTimeout(() => {
        this.aiResult = this.simulateAIAnalysis(inputText)
        this.isProcessing = false
        uni.hideLoading()
        uni.showToast({ title: '解析完成', icon: 'success' })
      }, 3000)
    },

    // 模拟AI分析（实际开发中应该调用真实的大模型API）
    simulateAIAnalysis(text) {
      // 这里模拟大模型的分析结果
      // 实际开发中应该调用如GPT、Claude等大模型API

      const result = {
        title: '',
        days: [],
        totalPoints: 0,
        totalExpense: 0
      }

      // 简单的关键词提取和结构化处理
      const sentences = text.split(/[。！？.!?]/).filter(s => s.trim())

      // 提取时间信息
      const timePatterns = [
        /今天|昨天|前天/g,
        /上午|下午|中午|晚上|早上/g,
        /\d{1,2}[点时:]\d{0,2}/g,
        /第[一二三四五六七八九十\d]+天/g
      ]

      // 提取地点信息
      const placePatterns = [
        /去了?([^，。,.\s]{2,10})/g,
        /到了?([^，。,.\s]{2,10})/g,
        /在([^，。,.\s]{2,10})/g
      ]

      // 提取消费信息
      const expensePatterns = [
        /花了?(\d+)块?钱?/g,
        /消费了?(\d+)元?/g,
        /(\d+)元/g
      ]

      // 分析每个句子
      let currentDay = {
        date: new Date().toISOString().split('T')[0],
        points: []
      }

      let totalExpense = 0

      sentences.forEach((sentence, index) => {
        const point = {
          place: '',
          arriveTime: '',
          desc: sentence.trim(),
          expenses: [],
          media: []
        }

        // 提取地点
        placePatterns.forEach(pattern => {
          const matches = sentence.match(pattern)
          if (matches && matches.length > 0) {
            point.place = matches[0].replace(/(去了?|到了?|在)/, '').trim()
          }
        })

        // 提取时间
        const timeMatch = sentence.match(/\d{1,2}[点时:]\d{0,2}/)
        if (timeMatch) {
          point.arriveTime = timeMatch[0].replace(/[点时]/, ':')
          if (!point.arriveTime.includes(':')) {
            point.arriveTime += ':00'
          }
        }

        // 提取消费
        expensePatterns.forEach(pattern => {
          const matches = sentence.match(pattern)
          if (matches) {
            matches.forEach(match => {
              const amount = parseInt(match.replace(/[^\d]/g, ''))
              if (amount > 0) {
                point.expenses.push({
                  amount: amount,
                  category: '其他',
                  note: '通过AI识别'
                })
                totalExpense += amount
              }
            })
          }
        })

        // 如果提取到了地点，就添加这个点位
        if (point.place || point.desc.length > 5) {
          if (!point.place) {
            point.place = `地点${currentDay.points.length + 1}`
          }
          currentDay.points.push(point)
        }
      })

      if (currentDay.points.length > 0) {
        result.days.push(currentDay)
      }

      // 生成标题
      const places = result.days.flatMap(day => day.points.map(p => p.place)).filter(p => p && !p.startsWith('地点'))
      result.title = places.length > 0 ? `${places.slice(0, 3).join('、')}之旅` : '我的旅行'

      result.totalPoints = result.days.reduce((sum, day) => sum + day.points.length, 0)
      result.totalExpense = totalExpense

      return result
    },

    // 语音录入相关
    toggleRecording() {
      this.isRecording = !this.isRecording

      if (this.isRecording) {
        uni.showToast({ title: '开始录音', icon: 'none' })
        // 实际开发中这里应该调用语音识别API
        // 这里模拟录音过程
      } else {
        uni.showLoading({ title: '语音识别中...' })
        // 模拟语音识别
        setTimeout(() => {
          uni.hideLoading()
          this.voiceText = '今天上午十点从酒店出发，先去了外滩看黄浦江，风景很美。中午在南京路步行街吃了小笼包，花了八十块钱。下午去了豫园逛街，买了纪念品，总共花了两百块。'
          uni.showToast({ title: '语音识别完成', icon: 'success' })
        }, 2000)
      }
    },

    useVoiceResult() {
      this.aiInput = this.voiceText
      this.inputMethod = 'text'
      uni.showToast({ title: '已将语音结果转为文字', icon: 'success' })
    },

    // 确认AI解析结果
    confirmAIResult() {
      if (!this.aiResult) {
        uni.showToast({ title: '没有可用的解析结果', icon: 'none' })
        return
      }

      uni.showLoading({ title: '应用结果中...' })

      try {
        // 更新旅行标题
        if (this.aiResult.title) {
          this.travelData.title = this.aiResult.title
        }

        // 应用AI解析的天数和点位数据
        this.aiResult.days.forEach((aiDay, dayIndex) => {
          // 如果当前天数不存在，创建新的一天
          if (!this.travelData.travel[dayIndex]) {
            this.addDay()
          }

          const currentDay = this.travelData.travel[dayIndex]

          // 更新日期
          if (aiDay.date) {
            currentDay.date = aiDay.date
          }

          // 添加AI识别的点位
          aiDay.points.forEach(aiPoint => {
            const newPoint = {
              id: Date.now() + Math.random(),
              place: aiPoint.place,
              arriveTime: aiPoint.arriveTime,
              desc: aiPoint.desc,
              expenses: aiPoint.expenses || [],
              media: aiPoint.media || []
            }

            currentDay.points.push(newPoint)
          })
        })

        // 移除错误的 this.autoSave() 调用，因为该方法不存在
        // AI智能录入后的数据已经直接添加到 travelDays 中，无需额外保存

        // 关闭模态框
        this.closeSmartModal()

        uni.hideLoading()
        uni.showToast({
          title: `成功添加${this.aiResult.totalPoints}个点位`,
          icon: 'success'
        })

      } catch (error) {
        uni.hideLoading()
        console.error('应用AI结果失败:', error)
        uni.showToast({ title: '应用结果失败', icon: 'error' })
      }
    },

    // 城市编辑相关方法
    openCityModal(dayIndex) {
      this.editingCityIndex = dayIndex
      this.currentCity = {
        city: this.travelDays[dayIndex].city || '',
        points: [...(this.travelDays[dayIndex].points || [])]
      }
      this.showCityModal = true
    },

    closeCityModal() {
      this.showCityModal = false
      this.editingCityIndex = -1
      this.currentCity = {
        city: '',
        points: []
      }
    },

    saveCityChanges() {
      if (this.editingCityIndex >= 0 && this.editingCityIndex < this.travelDays.length) {
        this.travelDays[this.editingCityIndex].city = this.currentCity.city
        this.travelDays[this.editingCityIndex].points = [...this.currentCity.points]
      }
      this.closeCityModal()
    },

    onCityNameChange() {
      // 城市名称变化时的处理逻辑
      // 可以在这里添加自动完成或验证逻辑
    },

    // 地图选点相关方法
    openMapPicker() {
      this.showMapModal = true
      this.resetMapData()
      this.initMapCenter()

      // 延迟初始化地图上下文，确保地图组件已渲染
      this.$nextTick(() => {
        this.initMapContext()
        // 自动获取当前位置附近的POI
        this.getCurrentLocationPOI()
      })
    },

    closeMapModal() {
      this.showMapModal = false
      this.resetMapData()
    },

    resetMapData() {
      this.poiKeyword = ''
      this.poiList = []
      this.selectedPOI = null
      this.isLoadingPOI = false
      this.hasSearched = false
      this.mapMarkers = []
    },

    // 初始化地图中心点
    initMapCenter() {
      // 确保配置已加载
      this.ensureConfigLoaded()

      // 使用配置文件中的默认中心点
      this.mapCenter = {
        lat: this.config.map.defaultCenter.lat,
        lng: this.config.map.defaultCenter.lng
      }
      this.mapScale = this.config.map.defaultScale
    },

    // 初始化地图上下文
    initMapContext() {
      this.mapContext = uni.createMapContext('smartInputMap', this)
    },

    // 获取当前位置附近的POI
    getCurrentLocationPOI() {
      this.isLoadingPOI = true
      this.hasSearched = true

      // 检查位置权限
      uni.getSetting({
        success: (res) => {
          if (!res.authSetting['scope.userLocation']) {
            // 请求位置权限
            uni.authorize({
              scope: 'scope.userLocation',
              success: () => {
                this.getLocationAndSearch()
              },
              fail: () => {
                uni.showModal({
                  title: '位置权限',
                  content: '需要位置权限才能获取附近地点，是否前往设置？',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      uni.openSetting()
                    } else {
                      // 使用默认位置
                      this.useDefaultLocationAndSearch()
                    }
                  }
                })
              }
            })
          } else {
            this.getLocationAndSearch()
          }
        }
      })
    },

    // 获取位置并搜索
    getLocationAndSearch() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 更新地图中心点
          this.mapCenter = {
            lat: res.latitude,
            lng: res.longitude
          }
          // 移动地图到当前位置
          if (this.mapContext) {
            this.mapContext.moveToLocation({
              latitude: res.latitude,
              longitude: res.longitude
            })
          }
          // 搜索附近POI
          this.searchNearbyPOI(res.longitude, res.latitude)
        },
        fail: (err) => {
          console.error('获取位置失败:', err)
          uni.showToast({ title: '获取位置失败，使用默认位置', icon: 'none' })
          this.useDefaultLocationAndSearch()
        }
      })
    },

    // 使用默认位置搜索
    useDefaultLocationAndSearch() {
      // 确保配置已加载
      this.ensureConfigLoaded()

      this.mapCenter = {
        lat: this.config.map.defaultCenter.lat,
        lng: this.config.map.defaultCenter.lng
      }
      this.searchNearbyPOI(this.config.map.defaultCenter.lng, this.config.map.defaultCenter.lat)
    },

    // 搜索附近POI（真实高德地图API）
    searchNearbyPOI(lng, lat, keyword = '') {
      this.isLoadingPOI = true

      // 确保配置已加载
      if (!this.ensureConfigLoaded()) {
        console.error('高德地图配置无效，使用降级方案')
        this.handlePOISearchError('配置加载失败')
        return
      }

      // 构建请求参数
      const requestData = {
        key: this.config.amap.key,
        location: `${lng},${lat}`,
        radius: this.config.amap.defaultSearch.radius,
        offset: this.config.amap.defaultSearch.offset,
        page: this.config.amap.defaultSearch.page,
        extensions: this.config.amap.defaultSearch.extensions
      }

      // 如果有关键词，使用文本搜索接口
      const apiUrl = keyword ?
        `${this.config.amap.baseUrl}/v3/place/text` :
        `${this.config.amap.baseUrl}/v3/place/around`

      if (keyword) {
        requestData.keywords = keyword
        requestData.city = '全国' // 搜索范围
      }

      console.log('调用高德POI搜索API:', apiUrl, requestData)

      uni.request({
        url: apiUrl,
        method: 'GET',
        data: requestData,
        success: (res) => {
          console.log('高德POI搜索结果:', res.data)

          if (res.data.status === '1' && res.data.pois && res.data.pois.length > 0) {
            // 处理高德API返回的数据
            this.poiList = res.data.pois.map(poi => {
              const [poiLng, poiLat] = poi.location.split(',').map(Number)
              return {
                id: poi.id,
                name: poi.name,
                address: poi.address || poi.adname,
                type: poi.type,
                typecode: poi.typecode,
                distance: poi.distance,
                lat: poiLat,
                lng: poiLng,
                tel: poi.tel,
                adname: poi.adname,
                cityname: poi.cityname
              }
            })

            // 更新地图标记
            this.updateMapMarkers()

            uni.showToast({
              title: `找到${this.poiList.length}个地点`,
              icon: 'success'
            })
          } else {
            console.warn('高德API返回异常:', res.data)
            this.handlePOISearchError(res.data.info || '搜索失败')
          }
        },
        fail: (err) => {
          console.error('高德POI搜索API调用失败:', err)
          this.handlePOISearchError('网络请求失败')
        },
        complete: () => {
          this.isLoadingPOI = false
        }
      })
    },

    // 处理POI搜索错误
    handlePOISearchError(errorMsg) {
      console.log('POI搜索失败，使用降级方案:', errorMsg)

      // 如果是开发环境或配置了使用模拟数据，则使用模拟数据
      if (this.config.dev.debug || this.config.dev.useMockData) {
        this.loadFallbackPOIs()
        uni.showToast({
          title: '使用模拟数据',
          icon: 'none'
        })
      } else {
        this.poiList = []
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    },

    // 加载降级POI数据
    loadFallbackPOIs() {
      const mockPOIs = [
        {
          id: 'mock_1',
          name: '星巴克咖啡(南京东路店)',
          address: '黄浦区南京东路123号',
          type: '餐饮服务;咖啡厅',
          distance: '50',
          lat: this.mapCenter.lat + 0.001,
          lng: this.mapCenter.lng + 0.001
        },
        {
          id: 'mock_2',
          name: '上海外滩观景台',
          address: '黄浦区中山东一路外滩',
          type: '风景名胜;观景台',
          distance: '120',
          lat: this.mapCenter.lat + 0.002,
          lng: this.mapCenter.lng - 0.001
        },
        {
          id: 'mock_3',
          name: '南京路步行街',
          address: '黄浦区南京东路步行街',
          type: '购物服务;步行街',
          distance: '200',
          lat: this.mapCenter.lat - 0.001,
          lng: this.mapCenter.lng + 0.002
        }
      ]

      this.poiList = mockPOIs
      this.updateMapMarkers()
    },

    // 更新地图标记
    updateMapMarkers() {
      this.mapMarkers = this.poiList.map((poi, index) => ({
        id: poi.id || index,
        latitude: poi.lat,
        longitude: poi.lng,
        title: poi.name,
        iconPath: '/static/images/marker.svg',
        width: 30,
        height: 30,
        callout: {
          content: poi.name,
          color: '#000000',
          fontSize: 12,
          borderRadius: 4,
          bgColor: '#FFFFFF',
          padding: 8,
          display: 'BYCLICK'
        }
      }))
    },

    // 搜索POI
    searchPOI() {
      if (!this.poiKeyword.trim()) {
        this.getCurrentLocationPOI()
        return
      }

      this.hasSearched = true
      // 使用当前地图中心点进行搜索
      this.searchNearbyPOI(this.mapCenter.lng, this.mapCenter.lat, this.poiKeyword)
    },

    // 移动到当前位置
    moveToCurrentLocation() {
      if (this.mapContext) {
        this.mapContext.moveToLocation()
      }
      this.getCurrentLocationPOI()
    },

    // 刷新POI
    refreshPOI() {
      this.searchNearbyPOI(this.mapCenter.lng, this.mapCenter.lat)
    },

    // 地图事件处理
    onMapTap(e) {
      console.log('地图点击:', e)
      // 可以在这里处理地图点击事件，比如在点击位置搜索POI
    },

    onMarkerTap(e) {
      console.log('标记点击:', e)
      const markerId = e.detail.markerId
      const poi = this.poiList.find(p => p.id === markerId)
      if (poi) {
        this.selectPOI(poi)
      }
    },

    onRegionChange(e) {
      if (e.type === 'end') {
        // 地图区域变化结束，更新中心点
        if (this.mapContext) {
          this.mapContext.getCenterLocation({
            success: (res) => {
              this.mapCenter = {
                lat: res.latitude,
                lng: res.longitude
              }
            }
          })
        }
      }
    },

    // 选择POI
    selectPOI(poi) {
      this.selectedPOI = poi
      uni.showToast({
        title: `已选择：${poi.name}`,
        icon: 'success'
      })
    },

    // 确认POI选择
    confirmPOISelection() {
      if (!this.selectedPOI) {
        uni.showToast({ title: '请先选择一个地点', icon: 'none' })
        return
      }

      // 填充地点信息
      this.currentPoint.place = this.selectedPOI.name
      this.currentPoint.lat = this.selectedPOI.lat
      this.currentPoint.lng = this.selectedPOI.lng

      // 如果有地址信息，可以添加到描述中
      if (this.selectedPOI.address && !this.currentPoint.desc) {
        this.currentPoint.desc = `地址：${this.selectedPOI.address}`
      }

      // 如果有电话信息，也可以保存
      if (this.selectedPOI.tel) {
        this.currentPoint.tel = this.selectedPOI.tel
      }

      this.autoSavePoint()
      this.closeMapModal()

      uni.showToast({
        title: '地点选择成功',
        icon: 'success'
      })
    },

    // 辅助方法：简化POI类型显示
    getSimplifiedType(type) {
      if (!type) return ''

      // 高德POI类型通常是"大类;中类;小类"的格式，取第一个作为显示
      const types = type.split(';')
      return types[0] || type
    },

    // 辅助方法：格式化距离显示
    formatDistance(distance) {
      if (!distance) return ''

      const dist = parseInt(distance)
      if (dist < 1000) {
        return `${dist}m`
      } else {
        return `${(dist / 1000).toFixed(1)}km`
      }
    }
  }
}
</script>

<style>
/* 页面基础样式 */
page {
  height: 100%;
  background: #FFFFFF;
}

.record-container {
  min-height: 100vh;
  background: #FFFFFF;
  /* 为自定义底部导航栏预留空间，包含安全区域 */
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid #E9ECEF;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF6B35;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #6C757D;
  line-height: 1.4;
}

/* 时间线内容区域 */
.timeline-content {
  padding: 24rpx;
  background: #F8F9FA;
}

/* 时间线列表 */
.timeline-list {
  margin-bottom: 32rpx;
}

/* 时间线日期项 */
.timeline-day {
  margin-bottom: 32rpx;
}

/* Day标题区域 */
.day-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  border: 1rpx solid #E9ECEF;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.day-header:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.day-title-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.day-indicator {
  position: relative;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day-line {
  width: 4rpx;
  height: 24rpx;
  background: #FFE5DC;
}

.day-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #FF6B35;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 0 0 2rpx #FFE5DC;
  margin: 4rpx 0;
}

.day-title-content {
  flex: 1;
}

.day-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  line-height: 1.3;
  margin-bottom: 4rpx;
}

.day-subtitle {
  font-size: 24rpx;
  color: #6C757D;
  line-height: 1.4;
}

.day-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.day-action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #F8F9FA;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ADB5BD;
  font-size: 28rpx;
  transition: all 0.3s ease;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  outline: none;
  overflow: hidden;
  flex-shrink: 0;
}

.day-action-btn:active {
  background: #FFE5DC;
  color: #FF6B35;
}



/* Day内容区域 */
.day-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  border: 1rpx solid #E9ECEF;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  padding: 16rpx 0 0 0;
}

/* 当日统计 */
.day-stats {
  display: flex;
  padding: 8rpx 24rpx 16rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  gap: 24rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #05D592;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #9ca3af;
  font-weight: 500;
}

/* 点位区域 */
.points-area {
  padding: 0rpx 24rpx 24rpx 24rpx;
}

.points-list {
  margin-bottom: 16rpx;
}

.point-item {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.point-item:active {
  background: #EDF7F3;
  transform: translateY(-2rpx);
}

.point-content {
  width: 100%;
}

.point-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.point-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #FF6B35;
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.point-info {
  flex: 1;
  min-width: 0;
}

.point-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #212529;
  line-height: 1.3;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.point-meta {
  display: flex;
  gap: 12rpx;
}

.point-time,
.point-duration {
  font-size: 22rpx;
  color: #6C757D;
  font-weight: 500;
}

.point-delete {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #F8F9FA;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ADB5BD;
  font-size: 18rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.point-delete:active {
  background: #FFE5DC;
  color: #FF6B35;
}

.point-preview {
  margin: 8rpx 0;
}

.point-desc {
  font-size: 24rpx;
  color: #6C757D;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 68rpx;
}

.point-extras {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.point-media,
.point-expense {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #FF6B35;
  font-weight: 500;
}

.point-media .fas,
.point-expense .fas {
  font-size: 18rpx;
}

/* 添加点位区域 */
.add-point-area {
  background: #F8F9FA;
  border: 2rpx dashed #E9ECEF;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.add-point-area:active {
  background: #FFE5DC;
  border-color: #FF6B35;
  transform: scale(0.98);
}

.add-point-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  color: #FF6B35;
}

.add-point-content .fas {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.add-point-content text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 添加新一天 */
.add-day-section {
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.add-day-btn {
  width: 100%;
  background: #FFE5DC;
  color: #FF6B35;
  border: 2rpx dashed #FF6B35;
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.add-day-btn:active {
  background: #FFD6C7;
  transform: scale(0.98);
}

.add-day-btn .fas {
  font-size: 24rpx;
}

/* 日期显示区域 */
.day-date-area {
  margin-left: 16rpx;
  position: absolute;
  top: 50%;
  right: 30rpx; /* 给删除按钮留出空间 */
  transform: translateY(-50%);
  z-index: 10;
}

.day-date-display {
  padding: 8rpx 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #FF6B35;
  border: 1rpx solid #E9ECEF;
  min-width: 120rpx;
  text-align: center;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.day-date-display:active {
  background: #E9ECEF;
  transform: scale(0.98);
}

.day-date-display.no-date {
  background: #FFE5DC;
  border-color: #FF6B35;
  color: #FF6B35;
  font-weight: 600;
  animation: pulse-orange 2s infinite;
}

@keyframes pulse-orange {
  0% {
    background: #FFE5DC;
    border-color: #FF6B35;
  }
  50% {
    background: #FFD6C7;
    border-color: #E55A2B;
  }
  100% {
    background: #FFE5DC;
    border-color: #FF6B35;
  }
}





/* 操作按钮 */
.action-buttons {
  padding: 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
  background: #FFFFFF;
  margin: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #F8F9FA;
  color: #6C757D;
  border: 1rpx solid #E9ECEF;
}

.action-btn.secondary:active {
  background: #E9ECEF;
  transform: scale(0.98);
}

.action-btn.primary {
  background: #FF6B35;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 53, 0.25);
}

.action-btn.primary:active {
  transform: scale(0.98);
  background: #E55A2B;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.35);
}

.action-btn .fas {
  font-size: 24rpx;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  /* 确保模态框在底部导航栏之上 */
  z-index: 1100;
  padding: 24rpx;
  box-sizing: border-box;
}

.modal-content {
  background: #FFFFFF;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  /* 调整最大高度，为底部导航栏和安全区域预留空间 */
  max-height: calc(100vh - 200rpx - env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx 16rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  line-height: 1.3;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #F8F9FA;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 20rpx;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #EDF7F3;
  color: #05D592;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 16rpx 24rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 24rpx 24rpx 24rpx;
  border-top: 1rpx solid #f8f8f8;
}

.modal-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.modal-btn.secondary {
  background: #f8f8f8;
  color: #6b7280;
}

.modal-btn.secondary:active {
  background: #EDF7F3;
  color: #05D592;
}

.modal-btn.primary {
  background: linear-gradient(90deg, #05D592 0%, #4AEDC4 100%);
  color: #FFFFFF;
}

.modal-btn.primary:active {
  transform: scale(0.98);
}

/* 表单样式 */
.form-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 600;
  color: #05D592;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.section-action {
  background: #C3E8DA;
  color: #05D592;
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.3s ease;
}

.section-action:active {
  background: #B8E2D1;
  transform: scale(0.95);
}

.section-action .fas {
  font-size: 18rpx;
}

.form-group {
  margin-bottom: 20rpx;
}

.form-group.half {
  flex: 1;
}

.form-row {
  display: flex;
  gap: 16rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #1a1a1a;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.form-input {
  width: 100%;
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  box-sizing: border-box;
  min-height: 72rpx;
  line-height: 1.4;
}

.form-input:focus {
  border-color: #05D592;
  outline: none;
}

.form-textarea {
  width: 100%;
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  min-height: 120rpx;
  resize: none;
  box-sizing: border-box;
  line-height: 1.4;
}

.form-textarea:focus {
  border-color: #05D592;
  outline: none;
}

.input-with-action {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.input-with-action .form-input {
  flex: 1;
}

.input-action-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 8rpx;
  background: #C3E8DA;
  color: #05D592;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.input-action-btn:active {
  background: #B8E2D1;
  transform: scale(0.95);
}

.picker-display {
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  min-height: 72rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  line-height: 1.4;
}

/* 城市点位列表 */
.city-points {
  margin-top: 24rpx;
}

.points-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.points-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
}

.add-point-btn {
  background: #C3E8DA;
  color: #05D592;
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.3s ease;
}

.add-point-btn:active {
  background: #B8E2D1;
  transform: scale(0.95);
}

.add-point-btn .fas {
  font-size: 18rpx;
}

.point-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.point-item:active {
  background: #EDF7F3;
}

.point-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #05D592;
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.point-content {
  flex: 1;
}

.point-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
  margin-bottom: 4rpx;
}

.point-time {
  font-size: 22rpx;
  color: #9ca3af;
  font-weight: 500;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 20rpx;
  transition: all 0.3s ease;
}

.action-icon:active {
  background: #ffebee;
  color: #f44336;
}

/* 体验评分滑块 */
.experience-slider {
  padding: 16rpx 0;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
}

.label-left,
.label-right {
  font-size: 22rpx;
  color: #9ca3af;
  font-weight: 500;
}

/* 媒体网格 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
  margin-top: 16rpx;
}

.media-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
}

.media-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.media-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

/* 消费记录 */
.expenses-list {
  margin-top: 16rpx;
}

.expense-item {
  margin-bottom: 16rpx;
}

.expense-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.expense-amount {
  flex: 1;
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
  font-size: 24rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  min-height: 60rpx;
  box-sizing: border-box;
  line-height: 1.4;
}

.expense-category {
  flex: 1;
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
  font-size: 24rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  line-height: 1.4;
}

.expense-note {
  flex: 1.5;
  border: 1rpx solid #EDF7F3;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
  font-size: 24rpx;
  background: #FFFFFF;
  color: #1a1a1a;
  min-height: 60rpx;
  box-sizing: border-box;
  line-height: 1.4;
}

.expense-remove {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background: #ffebee;
  color: #f44336;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  transition: all 0.3s ease;
}

.expense-remove:active {
  background: #ffcdd2;
  transform: scale(0.95);
}

/* 点位模态框特殊样式 */
.point-modal {
  /* 为底部导航栏和安全区域预留空间 */
  max-height: calc(100vh - 160rpx - env(safe-area-inset-bottom));
}

.point-modal .modal-body {
  max-height: calc(100vh - 360rpx - env(safe-area-inset-bottom));
}

/* 输入框按钮样式 */
.input-action-btn.smart-btn {
  background: #FFE5DC;
  color: #FF6B35;
}

.input-action-btn.smart-btn:active {
  background: #FFD6C7;
}

.input-action-btn.map-btn {
  background: #E3F2FD;
  color: #2196F3;
}

.input-action-btn.map-btn:active {
  background: #BBDEFB;
}

/* AI智能录入模态框 */
.ai-smart-modal {
  /* 为底部导航栏和安全区域预留空间 */
  max-height: calc(100vh - 180rpx - env(safe-area-inset-bottom));
  width: 90%;
}

.ai-smart-modal .modal-body {
  max-height: calc(100vh - 380rpx - env(safe-area-inset-bottom));
  overflow-y: auto;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.ai-icon {
  font-size: 32rpx;
  color: #FF6B35;
}

.ai-intro {
  text-align: center;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.intro-text {
  font-size: 26rpx;
  color: #6C757D;
  line-height: 1.5;
}

/* 输入方式选择 */
.input-method-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.method-tab {
  flex: 1;
  padding: 20rpx 16rpx;
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #6C757D;
  transition: all 0.2s ease;
}

.method-tab.active {
  background: #FFE5DC;
  border-color: #FF6B35;
  color: #FF6B35;
}

.method-tab:active {
  transform: scale(0.98);
}

.method-tab .fas {
  font-size: 28rpx;
}

/* 输入区域 */
.input-section {
  margin-bottom: 24rpx;
}

.ai-text-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 16rpx;
  font-size: 28rpx;
  background: #FFFFFF;
  line-height: 1.6;
  box-sizing: border-box;
  resize: none;
}

.ai-text-input:focus {
  border-color: #FF6B35;
  outline: none;
}

.input-counter {
  text-align: right;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #ADB5BD;
}

/* 语音输入区域 */
.voice-input-area {
  text-align: center;
  padding: 40rpx 20rpx;
}

.ai-voice-btn {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: #FF6B35;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
  margin: 0 auto;
}

.ai-voice-btn:active {
  transform: scale(0.95);
}

.ai-voice-btn.recording {
  background: #DC3545;
  animation: pulse 1.5s infinite;
}

.ai-voice-btn .fas {
  font-size: 48rpx;
}

.voice-result {
  margin-top: 30rpx;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  text-align: left;
  border: 2rpx solid #E9ECEF;
}

.voice-label {
  font-size: 24rpx;
  color: #6C757D;
  margin-bottom: 12rpx;
}

.voice-content {
  font-size: 26rpx;
  color: #212529;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.use-voice-btn {
  width: 100%;
  padding: 16rpx;
  background: #FF6B35;
  color: #FFFFFF;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.use-voice-btn:active {
  background: #E55A2B;
  transform: scale(0.98);
}

/* AI结果展示 */
.ai-result-section {
  margin-top: 24rpx;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
  border: 2rpx solid #E9ECEF;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.result-icon {
  font-size: 28rpx;
  color: #FF6B35;
}

.result-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #212529;
}

.result-preview {
  margin-bottom: 24rpx;
}

.preview-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.preview-label {
  font-size: 26rpx;
  color: #6C757D;
  width: 120rpx;
  flex-shrink: 0;
}

.preview-value {
  font-size: 26rpx;
  color: #212529;
  font-weight: 500;
  flex: 1;
}

.result-details {
  border-top: 1rpx solid #E9ECEF;
  padding-top: 20rpx;
}

.day-preview {
  margin-bottom: 20rpx;
}

.day-preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF6B35;
  margin-bottom: 12rpx;
}

.points-preview {
  padding-left: 20rpx;
}

.point-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #F8F9FA;
}

.point-preview:last-child {
  border-bottom: none;
}

.point-name {
  font-size: 26rpx;
  color: #212529;
  flex: 1;
}

.point-time {
  font-size: 24rpx;
  color: #6C757D;
}

/* 模态框按钮样式 */
.modal-btn.ai-parse {
  background: #FF6B35;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.modal-btn.ai-parse:disabled {
  background: #ADB5BD;
  color: #FFFFFF;
  opacity: 0.6;
}

.modal-btn.ai-parse:active:not(:disabled) {
  background: #E55A2B;
  transform: scale(0.98);
}

/* 地图选点模态框样式 */
.map-modal {
  /* 为底部导航栏和安全区域预留空间 */
  max-height: calc(100vh - 180rpx - env(safe-area-inset-bottom));
  width: 95%;
}

.map-modal .modal-body {
  max-height: calc(100vh - 380rpx - env(safe-area-inset-bottom));
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 地图容器 */
.map-container {
  position: relative;
  height: 400rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.smart-map {
  width: 100%;
  height: 100%;
}

/* 地图搜索覆盖层 */
.map-search-overlay {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 10;
  display: flex;
  gap: 12rpx;
}

.map-search-overlay .search-bar {
  flex: 1;
  display: flex;
  gap: 8rpx;
}

.map-search-overlay .search-input {
  flex: 1;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 26rpx;
  backdrop-filter: blur(10rpx);
}

.map-search-overlay .search-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(33, 150, 243, 0.95);
  color: #FFFFFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
}

.map-search-overlay .current-location-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.95);
  color: #2196F3;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
}

/* POI列表头部 */
.poi-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 0 4rpx;
}

.list-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
}

.refresh-btn {
  width: 48rpx;
  height: 48rpx;
  background: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #2196F3;
  transition: all 0.2s ease;
}

.refresh-btn:active:not(:disabled) {
  background: #E3F2FD;
  transform: scale(0.95);
}

.refresh-btn:disabled {
  opacity: 0.6;
}

/* POI列表区域 */
.poi-list-section {
  max-height: 400rpx;
  overflow-y: auto;
}

.loading-indicator {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #6C757D;
  font-size: 28rpx;
}

.loading-indicator .fas {
  margin-right: 12rpx;
  color: #2196F3;
  font-size: 32rpx;
}

.empty-poi {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #ADB5BD;
  font-size: 28rpx;
}

.poi-list {
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #E9ECEF;
}

.poi-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid #F8F9FA;
  transition: all 0.2s ease;
  cursor: pointer;
}

.poi-item:last-child {
  border-bottom: none;
}

.poi-item:active {
  background: #F8F9FA;
}

.poi-item.selected {
  background: #E3F2FD;
  border-color: #2196F3;
}

.poi-info {
  flex: 1;
  min-width: 0;
}

.poi-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.poi-address {
  font-size: 24rpx;
  color: #6C757D;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.poi-tags {
  display: flex;
  gap: 8rpx;
}

.poi-tag {
  padding: 4rpx 12rpx;
  background: #E9ECEF;
  color: #6C757D;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.poi-distance {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #2196F3;
  font-weight: 500;
  flex-shrink: 0;
}

</style>

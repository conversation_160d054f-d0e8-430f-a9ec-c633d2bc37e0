# 新版旅行记录页面（垂直时间线版本）

## 概述

这是一个全新设计的旅行记录页面 (`pages/record-new/record-new.vue`)，采用**以天为单位**的组织方式，使用**垂直时间线布局**，参考现代旅行应用的设计理念，严格遵循UI设计语言规范。

## 核心设计理念

### 1. 以天为单位的组织方式
- **时间线导向**：旅行记录以天为基本单位，符合用户的真实旅行体验
- **垂直时间线布局**：采用上下滚动的垂直时间线，避免左右分栏造成的拥挤感
- **展开/收起设计**：每一天可以独立展开或收起，用户可以专注于当前编辑的日期

### 2. 分层信息架构
- **时间线概览**：Day标题显示日期、城市、点位数量等关键信息
- **展开详情**：点击Day标题展开该天的详细设置和点位列表
- **模态框编辑**：点位的详细编辑通过模态框完成

### 3. UI设计语言遵循
严格遵循 `/docs/ui-design-language.md` 中定义的设计规范：
- **颜色系统**：使用主绿色 `#05D592`、浅绿色 `#C3E8DA`、背景绿色 `#EDF7F3`
- **组件样式**：采用Linear风格的卡片设计，圆角 `16rpx`，微妙阴影
- **交互状态**：悬停、激活、禁用状态的视觉反馈
- **字体规范**：标题、正文、次要文字的层次清晰

## 功能特性

### 垂直时间线
1. **Day标题卡片**：每一天显示为独立的卡片，包含Day标题和概要信息
2. **时间线连接线**：视觉化的连接线和圆点，形成清晰的时间线
3. **展开/收起**：点击Day标题可以展开或收起该天的详细内容
4. **添加天数**：底部提供添加新一天的按钮
5. **删除天数**：每个Day标题右侧提供删除按钮（至少保留一天）

### Day详情区域（展开状态）
1. **当日统计**：只有在有点位时才显示，包含点位数量、消费总额、平均体验
2. **点位列表**：显示当日所有旅行点位的卡片列表
3. **点位预览**：每个点位显示序号、名称、时间、描述、照片数量、消费信息
4. **添加点位区域**：大面积的添加点位按钮，引导用户添加内容

注：日期设置在Day标题右侧，统计信息按需显示，避免在空状态下显示无意义的"0"值。

### 点位编辑模态框
1. **基本信息**：地点名称、到达时间、停留时长、描述
2. **定位功能**：一键获取当前位置
3. **体验评分**：-5到5的滑块评分
4. **照片管理**：添加、删除照片，支持多张照片
5. **消费记录**：详细的消费明细管理，支持分类和备注

## 数据结构

采用全新的以天为单位的数据结构：

```javascript
travelDays: [
  {
    date: '2024-01-01',    // 具体日期
    city: '北京',          // 当日所在城市
    points: [              // 当日的旅行点位
      {
        place: '',         // 地点名称
        arriveTime: '',    // 到达时间
        duration: '',      // 停留时长
        desc: '',          // 描述
        media: [],         // 媒体文件
        experience: 0,     // 体验评分
        expenses: [],      // 消费记录
        lat: null,         // 纬度
        lng: null          // 经度
      }
    ]
  }
]
```

### 向后兼容性
- **自动转换**：支持从旧格式（按城市组织）自动转换为新格式（按天组织）
- **双格式保存**：保存时同时生成新格式和旧格式数据，确保兼容性

## 技术实现

### 组件架构
- **Vue单文件组件**：模板、脚本、样式分离
- **响应式数据**：使用Vue的响应式系统管理状态
- **计算属性**：`selectedDay` 计算属性实时获取当前选中日期的数据
- **模态框管理**：通过状态控制点位编辑模态框的显示和隐藏

### 布局系统
- **垂直滚动布局**：采用单列垂直滚动，避免分栏造成的拥挤感
- **卡片式设计**：每个Day作为独立卡片，清晰分隔
- **展开/收起机制**：通过CSS transition实现流畅的展开收起动画
- **响应式设计**：适配不同屏幕宽度，充分利用屏幕空间

### 数据管理
- **新数据结构**：`travelDays` 数组，以天为单位组织数据
- **展开状态管理**：每个day对象包含 `expanded` 属性控制展开状态
- **格式转换**：支持旧格式到新格式的自动转换
- **双格式兼容**：保存时同时生成新旧两种格式

### 样式系统
- **垂直时间线**：使用连接线和圆点创建视觉化的时间线效果
- **卡片层次**：Day标题卡片和内容卡片的层次分明
- **展开动画**：使用CSS transition实现流畅的展开收起效果
- **交互反馈**：点击、悬停状态的视觉反馈

### 数据持久化
- **本地存储**：使用 `uni.getStorageSync` 和 `uni.setStorageSync`
- **向后兼容**：新格式数据同时保存旧格式以保持兼容性
- **数据转换**：`convertOldFormatToNew` 和 `convertNewFormatToOld` 方法
- **编辑模式支持**：支持新建和编辑已有记录

## 使用方法

### 访问新页面
1. 在首页点击"新版记录"按钮
2. 或直接导航到 `/pages/record-new/record-new`

### 编辑已有记录
```javascript
uni.navigateTo({ 
  url: `/pages/record-new/record-new?id=${recordId}` 
})
```

### 新建记录
```javascript
uni.navigateTo({ 
  url: '/pages/record-new/record-new' 
})
```

## 与原页面的区别

| 特性 | 原页面 | 新页面（垂直时间线版本） |
|------|--------|------------------------|
| 组织方式 | 按城市组织 | 按天组织，垂直时间线导向 |
| 界面布局 | 单页面垂直滚动 | 垂直时间线，展开/收起设计 |
| 信息展示 | 所有信息平铺展示 | 分层展示，展开查看详情 |
| 操作流程 | 直接在页面上编辑 | 展开日期 → 查看详情 → 编辑点位 |
| 时间管理 | 无明确的时间概念 | 以天为单位，清晰的时间线 |
| 视觉负担 | 较重，信息密集 | 轻量，按需展开信息 |
| 空间利用 | 固定布局 | 充分利用屏幕宽度，避免分栏拥挤 |
| 数据结构 | 按城市分组的点位 | 按日期分组的点位 |
| UI风格 | 传统表单风格 | 现代垂直时间线 + 卡片设计 |

## 未来扩展

1. **拖拽排序**：支持时间线中天数的拖拽重排和点位的拖拽重排
2. **批量操作**：支持批量删除、移动点位，批量设置日期
3. **时间线增强**：
   - 支持设置每天的开始和结束时间
   - 显示每天的总行程时间
   - 支持跨天的行程安排
4. **智能建议**：
   - 根据地理位置智能建议点位顺序
   - 基于时间和距离优化行程安排
   - 推荐附近的景点和餐厅
5. **可视化增强**：
   - 时间线上显示天气信息
   - 每日路线图预览
   - 消费趋势图表
6. **模板功能**：保存和应用旅行模板，支持按天复制行程
7. **协作编辑**：支持多人协作编辑同一个旅行记录
8. **导入导出**：支持从其他旅行应用导入行程数据

## 开发说明

- **文件位置**：`pages/record-new/record-new.vue`
- **路由配置**：已添加到 `pages.json`
- **依赖**：无额外依赖，使用uni-app原生组件
- **兼容性**：支持所有uni-app支持的平台

<template>
  <view class="map-container">
    <map :latitude="latitude" :longitude="longitude" :markers="markers" style="width: 100%; height: 600rpx;" />
  </view>
</template>
<script>
export default {
  data() {
    return {
      latitude: 31.2304, // 默认上海
      longitude: 121.4737,
      markers: []
    }
  },
  onLoad(options) {
    // 可根据 options.place 调用地理编码API获取经纬度
    // 这里只做静态演示
    this.markers = [{
      id: 1,
      latitude: this.latitude,
      longitude: this.longitude,
      title: options.place || ''
    }]
  }
}
</script>
<style>
.map-container { padding: 24rpx; }
</style> 
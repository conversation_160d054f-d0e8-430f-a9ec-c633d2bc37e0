<template>
	<view class="profile-page">
		<!-- 顶部背景装饰 -->
		<view class="header-bg">
			<view class="bg-decoration"></view>
		</view>

		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-info">
				<view class="avatar-container">
					<image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<view class="avatar-badge" v-if="userInfo.level">
						<text class="badge-text">Lv.{{ userInfo.level }}</text>
					</view>
				</view>
				<view class="user-details">
					<text class="user-name">{{ userInfo.name }}</text>
					<text class="user-desc">{{ userInfo.description }}</text>
					<view class="user-tags">
						<text class="tag" v-for="tag in userInfo.tags" :key="tag">{{ tag }}</text>
					</view>
				</view>
				<button class="edit-btn" @click="editProfile">
					<i class="edit-icon fas fa-edit"></i>
				</button>
			</view>
		</view>

		<!-- 统计数据卡片 -->
		<view class="stats-card">
			<view class="stats-header">
				<text class="stats-title">我的足迹</text>
				<text class="stats-subtitle">记录每一次美好的旅程</text>
			</view>
			<view class="stats-grid">
				<view class="stat-item" @click="goToRecords">
					<view class="stat-icon-bg record">
						<i class="stat-icon fas fa-book"></i>
					</view>
					<text class="stat-number">{{ stats.recordCount }}</text>
					<text class="stat-label">旅行记录</text>
				</view>
				<view class="stat-item" @click="goToStats">
					<view class="stat-icon-bg city">
						<i class="stat-icon fas fa-city"></i>
					</view>
					<text class="stat-number">{{ stats.cityCount }}</text>
					<text class="stat-label">到过城市</text>
				</view>
				<view class="stat-item" @click="goToStats">
					<view class="stat-icon-bg days">
						<i class="stat-icon fas fa-calendar-alt"></i>
					</view>
					<text class="stat-number">{{ stats.totalDays }}</text>
					<text class="stat-label">旅行天数</text>
				</view>
				<view class="stat-item" @click="goToStats">
					<view class="stat-icon-bg expense">
						<i class="stat-icon fas fa-dollar-sign"></i>
					</view>
					<text class="stat-number">{{ formatExpense(stats.totalExpense) }}</text>
					<text class="stat-label">总消费</text>
				</view>
			</view>
		</view>

		<!-- 快捷功能 -->
		<view class="quick-actions">
			<view class="section-header">
				<text class="section-title">快捷功能</text>
			</view>
			<view class="action-grid">
				<view class="action-item" @click="goToStats">
					<view class="action-icon-bg stats">
						<i class="action-icon fas fa-chart-bar"></i>
					</view>
					<text class="action-text">消费统计</text>
				</view>
				<view class="action-item" @click="goToSearch">
					<view class="action-icon-bg search">
						<i class="action-icon fas fa-search"></i>
					</view>
					<text class="action-text">搜索记录</text>
				</view>
				<view class="action-item" @click="goToMap">
					<view class="action-icon-bg map">
						<i class="action-icon fas fa-map"></i>
					</view>
					<text class="action-text">地图查看</text>
				</view>
				<view class="action-item" @click="goToShare">
					<view class="action-icon-bg share">
						<i class="action-icon fas fa-share"></i>
					</view>
					<text class="action-text">分享记录</text>
				</view>
			</view>
		</view>

		<!-- 最近记录 -->
		<view class="recent-records" v-if="recentRecords.length > 0">
			<view class="section-header">
				<text class="section-title">最近记录</text>
				<text class="section-more" @click="goToRecords">查看全部</text>
			</view>
			<view class="record-list">
				<view class="record-item" v-for="record in recentRecords" :key="record.id" @click="goToDetail(record.id)">
					<image class="record-cover" :src="record.coverImage" mode="aspectFill"></image>
					<view class="record-info">
						<text class="record-title">{{ record.title }}</text>
						<text class="record-date">{{ formatDate(record.date) }}</text>
					</view>
					<text class="record-arrow">›</text>
				</view>
			</view>
		</view>

		<!-- 设置菜单 -->
		<view class="settings-section">
			<view class="section-header">
				<text class="section-title">设置</text>
			</view>
			<view class="menu-list">
				<view class="menu-item" @click="showAbout">
					<view class="menu-icon-bg about">
						<i class="menu-icon fas fa-info-circle"></i>
					</view>
					<text class="menu-title">关于应用</text>
					<view class="menu-right">
						<text class="menu-version">v1.0.0</text>
						<text class="menu-arrow">›</text>
					</view>
				</view>
				<view class="menu-item" @click="showFeedback">
					<view class="menu-icon-bg feedback">
						<i class="menu-icon fas fa-comment"></i>
					</view>
					<text class="menu-title">意见反馈</text>
					<text class="menu-arrow">›</text>
				</view>
				<view class="menu-item" @click="showPrivacy">
					<view class="menu-icon-bg privacy">
						<i class="menu-icon fas fa-lock"></i>
					</view>
					<text class="menu-title">隐私政策</text>
					<text class="menu-arrow">›</text>
				</view>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar :current="2"></custom-tabbar>
	</view>
</template>

<script>
	import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				userInfo: {
					name: '旅行达人',
					description: '世界那么大，我想去看看 ✈️',
					avatar: '/static/default-avatar.svg',
					level: 5,
					tags: ['摄影爱好者', '美食探索', '自由行']
				},
				stats: {
					recordCount: 0,
					cityCount: 0,
					totalDays: 0,
					totalExpense: 0
				},
				recentRecords: []
			}
		},

		onShow() {
			this.loadUserData()
		},

		methods: {
			loadUserData() {
				this.loadUserStats()
				this.loadRecentRecords()
			},

			loadUserStats() {
				const records = uni.getStorageSync('records') || []
				this.stats.recordCount = records.length

				// 计算统计数据
				const cities = new Set()
				let totalDays = 0
				let totalExpense = 0

				records.forEach(record => {
					if (record.travel && Array.isArray(record.travel)) {
						record.travel.forEach(city => {
							if (city.city) cities.add(city.city)
							if (city.points && Array.isArray(city.points)) {
								totalDays += city.points.length
								// 计算消费
								city.points.forEach(point => {
									if (point.expenses && Array.isArray(point.expenses)) {
										point.expenses.forEach(expense => {
											totalExpense += parseFloat(expense.amount) || 0
										})
									}
								})
							}
						})
					}
				})

				this.stats.cityCount = cities.size
				this.stats.totalDays = totalDays
				this.stats.totalExpense = totalExpense
			},

			loadRecentRecords() {
				const records = uni.getStorageSync('records') || []
				// 获取最近3条记录
				this.recentRecords = records
					.sort((a, b) => new Date(b.date) - new Date(a.date))
					.slice(0, 3)
					.map(record => ({
						id: record.id,
						title: record.title || '无标题',
						date: record.date,
						coverImage: this.getCoverImage(record)
					}))
			},

			getCoverImage(record) {
				// 获取封面图片
				if (record.coverImage) return record.coverImage

				const images = this.extractImages(record)
				if (images.length > 0) return images[0]

				return '/static/default-cover.svg'
			},

			extractImages(record) {
				// 从旅行记录中提取图片
				const images = []
				if (record.travel && Array.isArray(record.travel)) {
					record.travel.forEach(city => {
						if (city.points && Array.isArray(city.points)) {
							city.points.forEach(point => {
								if (point.media && Array.isArray(point.media)) {
									images.push(...point.media)
								}
							})
						}
					})
				}
				return images.slice(0, 9)
			},

			formatExpense(amount) {
				if (amount >= 10000) {
					return (amount / 10000).toFixed(1) + 'w'
				} else if (amount >= 1000) {
					return (amount / 1000).toFixed(1) + 'k'
				}
				return amount.toString()
			},

			formatDate(dateStr) {
				if (!dateStr) return ''
				const date = new Date(dateStr)
				const now = new Date()
				const diff = now - date
				const days = Math.floor(diff / (1000 * 60 * 60 * 24))

				if (days === 0) return '今天'
				if (days === 1) return '昨天'
				if (days < 7) return `${days}天前`
				if (days < 30) return `${Math.floor(days / 7)}周前`
				if (days < 365) return `${Math.floor(days / 30)}个月前`
				return `${Math.floor(days / 365)}年前`
			},

			editProfile() {
				uni.showToast({ title: '编辑功能开发中', icon: 'none' })
			},

			goToRecords() {
				uni.reLaunch({ url: '/pages/index/index' })
			},

			goToDetail(id) {
				// 直接进入编辑模式
				uni.navigateTo({ url: `/pages/record-new/record-new?id=${id}` })
			},

			goToStats() {
				uni.navigateTo({ url: '/pages/stats/stats' })
			},

			goToSearch() {
				uni.navigateTo({ url: '/pages/search/search' })
			},

			goToMap() {
				uni.navigateTo({ url: '/pages/map/map' })
			},

			goToShare() {
				uni.navigateTo({ url: '/pages/share/share' })
			},

			showAbout() {
				uni.showModal({
					title: '关于旅行记录',
					content: '一个简洁优雅的旅行记录应用，帮助你记录每一次美好的旅程。\n\n版本：v1.0.0\n开发者：旅行记录团队',
					showCancel: false
				})
			},

			showFeedback() {
				uni.showActionSheet({
					itemList: ['邮件反馈', '在线客服', '用户群'],
					success: (res) => {
						const actions = ['邮件反馈', '在线客服', '用户群']
						uni.showToast({
							title: `${actions[res.tapIndex]}功能开发中`,
							icon: 'none'
						})
					}
				})
			},

			showPrivacy() {
				uni.showModal({
					title: '隐私政策',
					content: '我们重视您的隐私保护。本应用仅在本地存储您的旅行记录，不会上传到服务器。',
					showCancel: false
				})
			}
		}
	}
</script>

<style>
	.profile-page {
		min-height: 100vh;
		background: #F8F9FA;
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
	}

	/* 顶部背景装饰 */
	.header-bg {
		height: 200rpx;
		background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
		position: relative;
		overflow: hidden;
	}

	.bg-decoration {
		position: absolute;
		top: -50rpx;
		right: -50rpx;
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
	}

	.bg-decoration::before {
		content: '';
		position: absolute;
		top: 50rpx;
		left: 50rpx;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
	}

	/* 用户信息卡片 */
	.user-card {
		background: #FFFFFF;
		margin: -80rpx 24rpx 32rpx;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
		position: relative;
		z-index: 10;
	}

	.user-info {
		display: flex;
		align-items: flex-start;
	}

	.avatar-container {
		position: relative;
		margin-right: 24rpx;
	}

	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #FFFFFF;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.avatar-badge {
		position: absolute;
		bottom: -8rpx;
		right: -8rpx;
		background: #FF6B35;
		color: #FFFFFF;
		padding: 4rpx 12rpx;
		border-radius: 16rpx;
		font-size: 20rpx;
		font-weight: 600;
		box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
	}

	.user-details {
		flex: 1;
		padding-top: 8rpx;
	}

	.user-name {
		font-size: 40rpx;
		font-weight: 700;
		color: #212529;
		margin-bottom: 8rpx;
	}

	.user-desc {
		font-size: 28rpx;
		color: #6C757D;
		line-height: 1.4;
		margin-bottom: 16rpx;
	}

	.user-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 12rpx;
	}

	.tag {
		background: #FFE5DC;
		color: #FF6B35;
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: 500;
	}

	.edit-btn {
		width: 72rpx;
		height: 72rpx;
		border-radius: 36rpx;
		background: #FFE5DC;
		color: #FF6B35;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
		margin-top: 8rpx;
	}

	.edit-btn:active {
		background: #FFD6C7;
		transform: scale(0.95);
	}

	.edit-icon {
		font-size: 32rpx;
	}

	/* 统计数据卡片 */
	.stats-card {
		background: #FFFFFF;
		margin: 0 24rpx 32rpx;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	}

	.stats-header {
		text-align: center;
		margin-bottom: 32rpx;
	}

	.stats-title {
		font-size: 36rpx;
		font-weight: 700;
		color: #212529;
		margin-bottom: 8rpx;
	}

	.stats-subtitle {
		font-size: 26rpx;
		color: #6C757D;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 24rpx;
	}

	.stat-item {
		text-align: center;
		padding: 24rpx 16rpx;
		border-radius: 20rpx;
		background: #F8F9FA;
		transition: all 0.2s ease;
	}

	.stat-item:active {
		transform: scale(0.95);
		background: #E9ECEF;
	}

	.stat-icon-bg {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 16rpx;
	}

	.stat-icon-bg.record {
		background: #E3F2FD;
		color: #2196F3;
	}
	.stat-icon-bg.city {
		background: #F3E5F5;
		color: #9C27B0;
	}
	.stat-icon-bg.days {
		background: #E8F5E8;
		color: #4CAF50;
	}
	.stat-icon-bg.expense {
		background: #FFF3E0;
		color: #FF9800;
	}

	.stat-icon {
		font-size: 36rpx;
	}

	.stat-number {
		font-size: 40rpx;
		font-weight: 700;
		color: #212529;
		margin-bottom: 8rpx;
	}

	.stat-label {
		font-size: 24rpx;
		color: #6C757D;
		font-weight: 500;
	}

	/* 通用区块样式 */
	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
		padding: 0 8rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #212529;
	}

	.section-more {
		font-size: 26rpx;
		color: #FF6B35;
		font-weight: 500;
	}

	/* 快捷功能 */
	.quick-actions {
		margin: 0 24rpx 32rpx;
	}

	.action-grid {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		gap: 20rpx;
	}

	.action-item {
		text-align: center;
		padding: 24rpx 16rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
		transition: all 0.2s ease;
	}

	.action-item:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.action-icon-bg {
		width: 72rpx;
		height: 72rpx;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 16rpx;
	}

	.action-icon-bg.stats {
		background: #E3F2FD;
		color: #2196F3;
	}
	.action-icon-bg.search {
		background: #F3E5F5;
		color: #9C27B0;
	}
	.action-icon-bg.map {
		background: #E8F5E8;
		color: #4CAF50;
	}
	.action-icon-bg.share {
		background: #FFF3E0;
		color: #FF9800;
	}

	.action-icon {
		font-size: 32rpx;
	}

	.action-text {
		font-size: 24rpx;
		color: #212529;
		font-weight: 500;
	}

	/* 最近记录 */
	.recent-records {
		margin: 0 24rpx 32rpx;
	}

	.record-list {
		background: #FFFFFF;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.record-item {
		display: flex;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #F8F9FA;
		transition: all 0.2s ease;
	}

	.record-item:last-child {
		border-bottom: none;
	}

	.record-item:active {
		background: #F8F9FA;
	}

	.record-cover {
		width: 80rpx;
		height: 80rpx;
		border-radius: 12rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.record-info {
		flex: 1;
		min-width: 0;
	}

	.record-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #212529;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.record-date {
		font-size: 24rpx;
		color: #6C757D;
	}

	.record-arrow {
		font-size: 32rpx;
		color: #ADB5BD;
		font-weight: 300;
		margin-left: 16rpx;
	}

	/* 设置菜单 */
	.settings-section {
		margin: 0 24rpx 32rpx;
	}

	.menu-list {
		background: #FFFFFF;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #F8F9FA;
		transition: all 0.2s ease;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-item:active {
		background: #F8F9FA;
	}

	.menu-icon-bg {
		width: 64rpx;
		height: 64rpx;
		border-radius: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.menu-icon-bg.about {
		background: #E3F2FD;
		color: #2196F3;
	}
	.menu-icon-bg.feedback {
		background: #F3E5F5;
		color: #9C27B0;
	}
	.menu-icon-bg.privacy {
		background: #E8F5E8;
		color: #4CAF50;
	}

	.menu-icon {
		font-size: 28rpx;
	}

	.menu-title {
		flex: 1;
		font-size: 30rpx;
		color: #212529;
		font-weight: 500;
	}

	.menu-right {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.menu-version {
		font-size: 24rpx;
		color: #ADB5BD;
		font-weight: 400;
	}

	.menu-arrow {
		font-size: 32rpx;
		color: #ADB5BD;
		font-weight: 300;
	}
</style>

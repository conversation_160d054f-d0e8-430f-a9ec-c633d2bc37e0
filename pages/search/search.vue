<template>
  <view class="search-container">
    <!-- 筛选条件区 -->
    <view class="section">
      <view class="section-title">多维度筛选</view>
      <view class="form-row">
        <picker :range="cityList" @change="onCityChange">
          <view class="picker">{{ filter.city || '选择城市' }}</view>
        </picker>
        <input class="input" v-model="filter.place" placeholder="地点关键词" />
      </view>
      <view class="form-row">
        <input class="input" v-model="filter.foodType" placeholder="饮食类型" />
        <input class="input" v-model="filter.budgetMin" type="number" placeholder="最低预算" />
        <input class="input" v-model="filter.budgetMax" type="number" placeholder="最高预算" />
      </view>
      <button size="mini" @click="doSearch">搜索</button>
    </view>

    <!-- 搜索结果区 -->
    <view class="section">
      <view class="section-title">搜索结果</view>
      <view v-if="results.length === 0" class="empty-tip">暂无符合条件的旅行记录</view>
      <view v-for="item in results" :key="item.id" class="record-card" @click="goDetail(item.id)">
        <view class="record-title">{{ item.title }}</view>
        <view class="record-desc">{{ item.desc }}</view>
        <view class="record-meta">{{ item.date }} · {{ item.place }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      allRecords: [],
      cityList: [],
      filter: {
        city: '',
        place: '',
        foodType: '',
        budgetMin: '',
        budgetMax: ''
      },
      results: []
    }
  },
  onShow() {
    // 加载本地所有记录
    this.allRecords = uni.getStorageSync('records') || []
    this.cityList = Array.from(new Set(this.allRecords.flatMap(r => (r.travel || []).map(c => c.city)))).filter(Boolean)
    this.results = this.allRecords
  },
  methods: {
    onCityChange(e) {
      this.filter.city = this.cityList[e.detail.value]
    },
    doSearch() {
      this.results = this.allRecords.filter(r => {
        // 城市筛选
        if (this.filter.city && !(r.travel || []).some(c => c.city === this.filter.city)) return false
        // 地点关键词
        if (this.filter.place && !(r.travel || []).some(c => (c.points || []).some(p => p.place && p.place.includes(this.filter.place)))) return false
        // 饮食类型（在消费明细里找）
        if (this.filter.foodType && !(r.travel || []).some(c => (c.points || []).some(p => (p.expenses || []).some(e => e.category === '餐饮' && e.note && e.note.includes(this.filter.foodType))))) return false
        // 预算区间（总消费）
        let total = 0
        ;(r.travel || []).forEach(c => (c.points || []).forEach(p => (p.expenses || []).forEach(e => { total += Number(e.amount || 0) })))
        if (this.filter.budgetMin && total < Number(this.filter.budgetMin)) return false
        if (this.filter.budgetMax && total > Number(this.filter.budgetMax)) return false
        return true
      })
    },
    goDetail(id) {
      // 直接进入编辑模式
      uni.navigateTo({ url: `/pages/record-new/record-new?id=${id}` })
    }
  }
}
</script>

<style>
.search-container { padding: 32rpx 24rpx 120rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx 20rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #FF6B35; }
.form-row { display: flex; align-items: center; margin-bottom: 12rpx; }
.input { flex: 1; border: 1rpx solid #E9ECEF; border-radius: 12rpx; padding: 12rpx; font-size: 28rpx; margin-right: 12rpx; background: #fff; }
.picker { flex: 1; color: #333; font-size: 28rpx; padding: 8rpx 0; }
.record-card { margin: 20rpx 0; padding: 20rpx; background: #f9f9f9; border-radius: 16rpx; box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04); }
.record-title { font-size: 36rpx; font-weight: bold; color: #222; }
.record-desc { font-size: 28rpx; color: #666; margin: 12rpx 0 0 0; }
.record-meta { font-size: 24rpx; color: #999; margin-top: 8rpx; }
.empty-tip { text-align: center; color: #aaa; margin: 24rpx 0; }
</style> 
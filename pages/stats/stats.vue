<template>
  <view class="stats-container">
    <!-- 消费录入区 -->
    <view class="section">
      <view class="section-title">补充消费录入</view>
      <view class="form-row">
        <input class="input" v-model="newExpense.amount" type="number" placeholder="金额" />
        <picker :range="categoryList" @change="onCategoryChange">
          <view class="picker">{{ newExpense.category || '选择类别' }}</view>
        </picker>
      </view>
      <view class="form-row">
        <picker mode="date" :value="newExpense.date" @change="onDateChange">
          <view class="picker">{{ newExpense.date || '选择日期' }}</view>
        </picker>
        <input class="input" v-model="newExpense.note" placeholder="备注" />
      </view>
      <button size="mini" @click="addExpense">添加</button>
      <button size="mini" @click="ocrExpense">拍照识别小票</button>
    </view>

    <!-- 消费统计区 -->
    <view class="section">
      <view class="section-title">消费统计</view>
      <view class="stat-row">总消费：￥{{ totalExpense }}</view>
      <view class="stat-row">点位消费：￥{{ pointExpenseTotal }}</view>
      <view class="stat-row">补充消费：￥{{ supplementExpenseTotal }}</view>
      <view class="stat-row">按类别统计：</view>
      <view v-for="cat in categoryList" :key="cat" class="stat-row">
        {{ cat }}：￥{{ statByCategory(cat) }}
      </view>
      <view class="stat-row">按日期统计：</view>
      <view v-for="d in statDates" :key="d" class="stat-row">
        {{ d }}：￥{{ statByDate(d) }}
      </view>
      <view class="chart-placeholder">[图表占位，后续可接入uCharts/ECharts]</view>
    </view>

    <!-- 消费明细列表 -->
    <view class="section">
      <view class="section-title">消费明细</view>
      <view v-if="allExpenses.length === 0" class="empty-tip">暂无消费记录</view>
      <view v-for="(item, idx) in allExpenses" :key="idx" class="expense-item">
        <view class="expense-header">
          <view>{{ item.date }} | {{ item.category }} | ￥{{ item.amount }}</view>
          <view class="expense-source">{{ item.source === 'point' ? '点位消费' : '补充消费' }}</view>
        </view>
        <view class="expense-note">{{ item.place || item.note }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pointExpenses: [], // 来自旅行记录的点位消费
      supplementExpenses: [], // 补充消费
      newExpense: {
        amount: '',
        category: '',
        date: '',
        note: ''
      },
      categoryList: ['餐饮', '住宿', '市内交通', '大交通', '纪念品', '门票', '其他']
    }
  },
  computed: {
    allExpenses() {
      return [...this.pointExpenses, ...this.supplementExpenses].sort((a, b) => new Date(b.date) - new Date(a.date))
    },
    totalExpense() {
      return this.allExpenses.reduce((sum, e) => sum + Number(e.amount || 0), 0)
    },
    pointExpenseTotal() {
      return this.pointExpenses.reduce((sum, e) => sum + Number(e.amount || 0), 0)
    },
    supplementExpenseTotal() {
      return this.supplementExpenses.reduce((sum, e) => sum + Number(e.amount || 0), 0)
    },
    statDates() {
      return Array.from(new Set(this.allExpenses.map(e => e.date))).sort()
    }
  },
  onShow() {
    // 从旅行记录页面获取点位消费数据
    this.loadPointExpenses()
  },
  methods: {
    loadPointExpenses() {
      // 这里应该从旅行记录页面或本地存储获取点位消费数据
      // 暂时用模拟数据
      this.pointExpenses = [
        { amount: 120, category: '餐饮', date: '2024-06-01', place: '外滩餐厅', source: 'point' },
        { amount: 300, category: '住宿', date: '2024-06-01', place: '上海酒店', source: 'point' }
      ]
    },
    onCategoryChange(e) {
      this.newExpense.category = this.categoryList[e.detail.value]
    },
    onDateChange(e) {
      this.newExpense.date = e.detail.value
    },
    addExpense() {
      if (!this.newExpense.amount || !this.newExpense.category || !this.newExpense.date) {
        uni.showToast({ title: '请填写完整', icon: 'none' })
        return
      }
      this.supplementExpenses.push({ 
        ...this.newExpense, 
        source: 'supplement',
        amount: Number(this.newExpense.amount)
      })
      this.newExpense = { amount: '', category: '', date: '', note: '' }
    },
    ocrExpense() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          uni.showLoading({ title: '识别中...' })
          // 这里调用后端OCR接口
          setTimeout(() => {
            uni.hideLoading()
            // 模拟OCR识别结果
            const ocrResult = {
              amount: 85.50,
              category: '餐饮',
              date: new Date().toISOString().split('T')[0],
              note: '晚餐',
              source: 'supplement'
            }
            this.supplementExpenses.push(ocrResult)
            uni.showToast({ title: '识别成功', icon: 'success' })
          }, 2000)
        }
      })
    },
    statByCategory(cat) {
      return this.allExpenses.filter(e => e.category === cat).reduce((sum, e) => sum + Number(e.amount || 0), 0)
    },
    statByDate(date) {
      return this.allExpenses.filter(e => e.date === date).reduce((sum, e) => sum + Number(e.amount || 0), 0)
    }
  }
}
</script>

<style>
.stats-container { padding: 32rpx 24rpx 120rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx 20rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #FF6B35; }
.form-row { display: flex; align-items: center; margin-bottom: 12rpx; }
.input { flex: 1; border: 1rpx solid #E9ECEF; border-radius: 12rpx; padding: 12rpx; font-size: 28rpx; margin-right: 12rpx; background: #fff; }
.picker { flex: 1; color: #212529; font-size: 28rpx; padding: 8rpx 0; }
.stat-row { font-size: 28rpx; color: #212529; margin-bottom: 6rpx; }
.chart-placeholder { margin: 18rpx 0; color: #6C757D; text-align: center; font-size: 28rpx; }
.expense-item { padding: 12rpx 0; border-bottom: 1rpx solid #E9ECEF; font-size: 28rpx; }
.expense-header { display: flex; justify-content: space-between; align-items: center; }
.expense-source { font-size: 22rpx; color: #FF6B35; background: #FFE5DC; padding: 4rpx 8rpx; border-radius: 8rpx; }
.expense-note { color: #6C757D; font-size: 24rpx; margin-top: 2rpx; }
.empty-tip { text-align: center; color: #aaa; margin: 24rpx 0; }
</style> 
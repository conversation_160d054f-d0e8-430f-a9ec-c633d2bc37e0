<template>
  <view class="city-container">
    <view class="section">
      <view class="section-title">城市：{{ cityName }}</view>
      <input class="input" v-model="info.airport" placeholder="机场" />
      <input class="input" v-model="info.trainStation" placeholder="火车站" />
      <input class="input" v-model="info.pier" placeholder="码头" />
    </view>
    <view class="section">
      <view class="section-title">市内交通</view>
      <input class="input" v-model="info.metro" placeholder="公交地铁信息" />
      <input class="input" v-model="info.taxi" placeholder="打车信息" />
    </view>
    <view class="section">
      <view class="section-title">超市</view>
      <input class="input" v-model="info.market" placeholder="品牌/营业时间/注意事项" />
    </view>
    <view class="section">
      <view class="section-title">安全与注意事项</view>
      <input class="input" v-model="info.safety" placeholder="民风民情/生活习惯/防骗等" />
    </view>
    <view class="section">
      <view class="section-title">其他生活服务</view>
      <input class="input" v-model="info.service" placeholder="货币兑换/报警/大使馆等" />
    </view>
    <button type="primary" class="submit-btn" @click="save">保存城市信息</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cityName: '',
      info: {
        airport: '',
        trainStation: '',
        pier: '',
        metro: '',
        taxi: '',
        market: '',
        safety: '',
        service: ''
      }
    }
  },
  onLoad(options) {
    if (options.city) {
      this.cityName = options.city
      // 这里可根据城市名加载已保存的信息
    }
  },
  methods: {
    save() {
      uni.showToast({ title: '保存成功', icon: 'success' })
      // 后续可保存到后端或本地
    }
  }
}
</script>

<style>
.city-container { padding: 32rpx 24rpx 120rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx 20rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #05D592; }
.input { width: 100%; border: 1rpx solid #EDF7F3; border-radius: 8rpx; padding: 16rpx; font-size: 28rpx; margin-bottom: 12rpx; background: #fff; }
.submit-btn { margin-top: 32rpx; width: 100%; font-size: 32rpx; border-radius: 32rpx; }
</style> 
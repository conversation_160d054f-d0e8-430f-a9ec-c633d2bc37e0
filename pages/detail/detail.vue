<template>
  <view class="detail-container">
    <!-- 旅途基本信息 -->
    <view class="section">
      <view class="section-title">旅途信息</view>
      <view>旅途名称：{{ travel.name || '未命名旅途' }}</view>
      <view>出发日期：{{ travel.startDate || '-' }}</view>
      <view>涉及城市：{{ travel.cities.map(c => c.city).join('、') }}</view>
    </view>

    <!-- 城市与点位列表 -->
    <view v-for="(city, cidx) in travel.cities" :key="cidx" class="city-block">
      <view class="city-title">
        城市：{{ city.city }}
        <button size="mini" @click="goCityInfo(city.city)">城市信息</button>
      </view>
      <view v-for="(point, idx) in city.points" :key="idx" class="point-card">
        <view class="point-header">
          第{{ idx+1 }}站：{{ point.place }}
          <button size="mini" @click="goMap(point)">地图查看</button>
        </view>
        <view class="point-meta">到达时间：{{ point.arriveTime || '-' }}，停留：{{ point.duration || '-' }}</view>
        <view class="point-desc">{{ point.desc }}</view>
        <view class="media-list">
          <image v-for="(img, i) in point.media" :key="i" :src="img" class="media-img" @click="previewImage(point.media, i)" />
        </view>
        <!-- 点位消费明细 -->
        <view v-if="point.expenses && point.expenses.length" class="expense-section">
          <view class="expense-title">消费明细</view>
          <view v-for="(expense, eidx) in point.expenses" :key="eidx" class="expense-item">
            <view>{{ expense.category }}：￥{{ expense.amount }} <text v-if="expense.note">({{ expense.note }})</text></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 路线图占位 -->
    <view class="section">
      <view class="section-title">路线图</view>
      <view class="placeholder">（后续可自动生成路线图，当前为占位）</view>
    </view>

    <!-- 本次旅途消费统计 -->
    <view class="section">
      <view class="section-title">本次旅途消费统计</view>
      <view>总消费：￥{{ totalExpense }}</view>
      <view v-for="cat in expenseCategories" :key="cat" class="stat-row">
        {{ cat }}：￥{{ statByCategory(cat) }}
      </view>
      <view class="chart-placeholder">[图表占位，后续可接入uCharts/ECharts]</view>
      <view class="section-title" style="margin-top:24rpx;">消费明细</view>
      <view v-if="allExpenses.length === 0" class="empty-tip">暂无消费记录</view>
      <view v-for="(item, idx) in allExpenses" :key="idx" class="expense-item">
        <view>{{ item.date || '-' }} | {{ item.category }} | ￥{{ item.amount }} <text v-if="item.note">({{ item.note }})</text></view>
        <view class="expense-note">{{ item.place }}</view>
      </view>
    </view>
    <view class="section">
      <button class="share-btn" @click="goShare">
        <i class="fas fa-share"></i>
        <text>分享本次旅行</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      travel: {
        name: '',
        startDate: '',
        cities: []
      },
      expenseCategories: ['餐饮', '住宿', '市内交通', '大交通', '纪念品', '门票', '其他']
    }
  },
  onLoad(options) {
    if (options.id) {
      const records = uni.getStorageSync('records') || []
      const record = records.find(r => String(r.id) === String(options.id))
      if (record) {
        // 兼容旧数据结构
        if (Array.isArray(record.travel)) {
          this.travel = {
            name: record.title || '',
            startDate: record.date || '',
            cities: record.travel
          }
        } else {
          this.travel = record.travel
        }
      } else {
        uni.showToast({ title: '未找到该记录', icon: 'none' })
      }
    } else {
      uni.showToast({ title: '无效的记录ID', icon: 'none' })
    }
  },
  computed: {
    allExpenses() {
      // 汇总所有点位的消费
      const arr = []
      this.travel.cities.forEach(city => {
        city.points.forEach(point => {
          (point.expenses || []).forEach(exp => {
            arr.push({
              ...exp,
              place: point.place
            })
          })
        })
      })
      return arr
    },
    totalExpense() {
      return this.allExpenses.reduce((sum, e) => sum + Number(e.amount || 0), 0)
    }
  },
  methods: {
    statByCategory(cat) {
      return this.allExpenses.filter(e => e.category === cat).reduce((sum, e) => sum + Number(e.amount || 0), 0)
    },
    goCityInfo(cityName) {
      uni.navigateTo({ url: `/pages/city/city?city=${cityName}` })
    },
    goMap(point) {
      uni.navigateTo({ url: `/pages/map/map?place=${encodeURIComponent(point.place)}` })
    },
    previewImage(imgList, index) {
      uni.previewImage({
        urls: imgList,
        current: imgList[index]
      })
    },

    goShare() {
      // 获取当前记录id
      const id = this.$mp?.query?.id || (this.$route && this.$route.query && this.$route.query.id) || ''
      uni.navigateTo({ url: `/pages/share/share?id=${id}` })
    }
  }
}
</script>

<style>
.detail-container { padding: 32rpx 24rpx 120rpx 24rpx; }
.section { margin-bottom: 36rpx; background: #fff; border-radius: 16rpx; padding: 24rpx 20rpx; box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03); }
.section-title { font-size: 32rpx; font-weight: bold; margin-bottom: 18rpx; color: #FF6B35; }
.city-block { margin-bottom: 32rpx; border: 2rpx solid #E9ECEF; border-radius: 20rpx; background: #F8F9FA; padding: 18rpx 12rpx; box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04); }
.city-title { font-size: 30rpx; font-weight: bold; color: #FF6B35; margin-bottom: 10rpx; }
.point-card { margin-bottom: 18rpx; border: 1rpx solid #f9f9f9; border-radius: 12rpx; padding: 12rpx; background: #f9f9f9; }
.point-header { font-size: 28rpx; font-weight: bold; color: #333; margin-bottom: 4rpx; }
.point-meta { font-size: 24rpx; color: #888; margin-bottom: 4rpx; }
.point-desc { font-size: 26rpx; color: #444; margin-bottom: 6rpx; }
.media-list { display: flex; margin-bottom: 6rpx; }
.media-img { width: 80rpx; height: 80rpx; border-radius: 8rpx; margin-right: 8rpx; }
.expense-section { margin-top: 8rpx; padding: 8rpx; background: #FFE5DC; border-radius: 12rpx; }
.expense-title { font-size: 26rpx; font-weight: bold; margin-bottom: 6rpx; color: #FF6B35; }
.expense-item { font-size: 26rpx; color: #333; margin-bottom: 4rpx; }
.stat-row { font-size: 28rpx; color: #333; margin-bottom: 6rpx; }
.chart-placeholder { margin: 18rpx 0; color: #aaa; text-align: center; font-size: 28rpx; }
.empty-tip { text-align: center; color: #aaa; margin: 24rpx 0; }
.expense-note { color: #888; font-size: 24rpx; margin-top: 2rpx; }
.share-btn {
  width: 100%;
  margin-bottom: 18rpx;
  font-size: 30rpx;
  border-radius: 32rpx;
  background: #FF6B35;
  color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
</style> 
// 项目配置文件
// 用于管理API密钥和其他配置信息

const config = {
  // 高德地图Web API配置
  amap: {
    // 高德地图Web API Key
    // 注意：生产环境中应该使用环境变量或服务器端代理来保护API Key
    key: '3ffb35a19f3dd49a1026aba990fffb35',
    baseUrl: 'https://restapi.amap.com',
    // 默认搜索配置
    defaultSearch: {
      radius: 1000,        // 搜索半径（米）
      offset: 20,          // 每页记录数
      page: 1,             // 页码
      extensions: 'base'   // 返回结果详细程度
    }
  },

  // 地图默认配置
  map: {
    // 默认中心点（上海）
    defaultCenter: {
      lat: 31.2304,
      lng: 121.4737
    },
    // 默认缩放级别
    defaultScale: 16,
    // 搜索类型（POI分类）
    searchTypes: '050000|060000|070000|080000|090000|100000|110000|120000|130000|140000|150000|160000|170000|180000|190000|200000'
  },

  // 应用基础配置
  app: {
    name: 'travel-recorder',
    version: '1.0.0',
    // 数据存储键名
    storageKeys: {
      travelData: 'travelData'
    }
  },

  // 开发环境配置
  dev: {
    // 是否启用调试模式
    debug: true,
    // 是否使用模拟数据
    useMockData: false
  }
};

// 导出配置 - 使用更兼容的方式
export default config;

// 兼容CommonJS导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = config;
}

// 全局配置挂载 - 确保在所有环境下都能访问配置
console.log('开始挂载全局配置...');

// 挂载到uni全局对象
if (typeof uni !== 'undefined') {
  uni.$appConfig = config;
  console.log('✅ 配置已挂载到 uni.$appConfig');
}

// 挂载到App全局数据
if (typeof getApp === 'function') {
  try {
    const app = getApp();
    if (app) {
      app.globalData = app.globalData || {};
      app.globalData.config = config;
      console.log('✅ 配置已挂载到 app.globalData.config');
    }
  } catch (e) {
    console.log('App实例尚未创建，稍后会自动挂载');
  }
}

// 挂载到window对象（H5环境）
if (typeof window !== 'undefined') {
  window.appConfig = config;
  console.log('✅ 配置已挂载到 window.appConfig');
}

console.log('全局配置挂载完成:', config);

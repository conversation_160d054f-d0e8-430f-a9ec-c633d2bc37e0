# API参考文档 - 外部服务与接口

## 高德地图 Web API

### 基础配置
- **Base URL**: `https://restapi.amap.com`
- **API Key**: 通过配置文件管理（见config.js）
- **坐标系**: GCJ-02 (火星坐标系)

### 1. POI搜索接口
**接口地址**: `/v3/place/around`  
**方法**: GET  
**用途**: 周边POI搜索

**请求参数**:
```javascript
{
  key: config.amapKey,                        // API密钥（从配置文件获取）
  location: '116.397470,39.908823',           // 中心点坐标 (经度,纬度)
  radius: 1000,                               // 搜索半径，单位米
  offset: 20,                                 // 每页记录数
  page: 1,                                    // 页码
  extensions: 'base'                          // 返回结果详细程度
}
```

**响应格式**:
```javascript
{
  status: "1",        // 状态码，1表示成功
  count: "20",        // 返回结果数
  info: "OK",         // 状态信息
  pois: [             // POI列表
    {
      id: "B000A83M9D",
      name: "天安门",
      type: "风景名胜;风景名胜相关;风景名胜",
      typecode: "110000",
      address: "北京市东城区东长安街",
      location: "116.397470,39.908823",    // 经度,纬度
      tel: "",
      distance: "0",
      biz_type: "",
      adname: "东城区",
      cityname: "北京市",
      business_area: "",
      photos: []        // 照片数组
    }
  ]
}
```

**实现示例**:
```javascript
searchNearbyPOIs() {
  this.isLoadingPOI = true;

  uni.request({
    url: 'https://restapi.amap.com/v3/place/around',
    method: 'GET',
    data: {
      key: this.config.amapKey, // 使用配置文件中的API Key
      location: `${this.mapCenter.lng},${this.mapCenter.lat}`,
      radius: 1000,
      offset: 20,
      page: 1,
      extensions: 'base'
    },
    success: (res) => {
      if (res.data.status === '1' && res.data.pois) {
        this.nearbyPOIs = res.data.pois;
        uni.showToast({ title: `找到${this.nearbyPOIs.length}个附近地点`, icon: 'success' });
      } else {
        this.loadFallbackPOIs();
      }
    },
    fail: (err) => {
      console.error('高德Web API调用失败:', err);
      uni.showToast({ title: '网络错误，使用默认数据', icon: 'none' });
      this.loadFallbackPOIs();
    },
    complete: () => {
      this.isLoadingPOI = false;
    }
  });
}
```

### 2. 地理编码接口
**接口地址**: `/v3/geocode/geo`  
**方法**: GET  
**用途**: 地址转坐标

**请求参数**:
```javascript
{
  key: config.amapKey, // 从配置文件获取
  address: '北京市朝阳区阜通东大街6号'
}
```

## Uni-App 核心API

### 1. 数据存储
```javascript
// 同步存储
uni.setStorageSync('key', data);
const data = uni.getStorageSync('key');
uni.removeStorageSync('key');

// 异步存储
uni.setStorage({
  key: 'key',
  data: data,
  success: () => {}
});
```

### 2. 网络请求
```javascript
uni.request({
  url: 'https://api.example.com',
  method: 'GET',
  data: {},
  header: {
    'Content-Type': 'application/json'
  },
  success: (res) => {
    console.log(res.data);
  },
  fail: (err) => {
    console.error(err);
  }
});
```

### 3. 位置服务
```javascript
// 获取当前位置
uni.getLocation({
  type: 'gcj02',        // 坐标系类型
  success: (res) => {
    console.log('纬度：' + res.latitude);
    console.log('经度：' + res.longitude);
  }
});

// 选择位置
uni.chooseLocation({
  success: (res) => {
    console.log('位置名称：' + res.name);
    console.log('详细地址：' + res.address);
    console.log('纬度：' + res.latitude);
    console.log('经度：' + res.longitude);
  }
});
```

### 4. 地图组件API
```javascript
// 创建地图上下文
const mapContext = uni.createMapContext('smartInputMap', this);

// 获取地图中心点
mapContext.getCenterLocation({
  success: (res) => {
    console.log(res.longitude, res.latitude);
  }
});

// 移动到指定位置
mapContext.moveToLocation({
  latitude: 39.908823,
  longitude: 116.397470
});
```

### 5. 页面导航
```javascript
// 跳转到页面
uni.navigateTo({
  url: '/pages/detail/detail?id=123'
});

// 返回上一页
uni.navigateBack({
  delta: 1
});

// 获取页面栈
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];
const prevPage = pages[pages.length - 2];
```

### 6. 交互反馈
```javascript
// 显示消息提示
uni.showToast({
  title: '成功',
  icon: 'success',
  duration: 2000
});

// 显示加载提示
uni.showLoading({
  title: '加载中'
});
uni.hideLoading();

// 显示确认对话框
uni.showModal({
  title: '提示',
  content: '确认删除吗？',
  success: (res) => {
    if (res.confirm) {
      console.log('用户点击确定');
    }
  }
});
```

### 7. 媒体选择
```javascript
// 选择图片
uni.chooseImage({
  count: 9,
  sizeType: ['original', 'compressed'],
  sourceType: ['album', 'camera'],
  success: (res) => {
    console.log(res.tempFilePaths);
  }
});

// 预览图片
uni.previewImage({
  urls: ['image1.jpg', 'image2.jpg'],
  current: 'image1.jpg'
});
```

## 错误处理规范

### 网络错误处理
```javascript
async function handleRequest(requestPromise) {
  try {
    const response = await requestPromise;
    return response.data;
  } catch (error) {
    if (error.errMsg && error.errMsg.includes('timeout')) {
      uni.showToast({ title: '网络超时，请重试', icon: 'none' });
    } else if (error.errMsg && error.errMsg.includes('fail')) {
      uni.showToast({ title: '网络连接失败', icon: 'none' });
    } else {
      uni.showToast({ title: '请求失败', icon: 'none' });
    }
    throw error;
  }
}
```

### API状态码处理
```javascript
function handleAmapResponse(response) {
  switch (response.data.status) {
    case '1':
      return response.data;
    case '0':
      throw new Error('请求失败：' + response.data.info);
    case '10001':
      throw new Error('API Key无效');
    case '10002':
      throw new Error('请求超限');
    case '10003':
      throw new Error('请求参数错误');
    default:
      throw new Error('未知错误：' + response.data.info);
  }
}
```

## 权限配置

### manifest.json配置
```json
{
  "mp-weixin": {
    "requiredPrivateInfos": ["getLocation"],
    "permission": {
      "scope.userLocation": {
        "desc": "获取位置信息用于记录旅行轨迹"
      }
    }
  }
}
```

### 动态权限申请
```javascript
// 检查位置权限
uni.getSetting({
  success: (res) => {
    if (!res.authSetting['scope.userLocation']) {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          // 用户同意授权
          this.getCurrentLocation();
        },
        fail: () => {
          // 用户拒绝授权
          uni.showModal({
            title: '提示',
            content: '需要位置权限才能使用此功能',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
    } else {
      this.getCurrentLocation();
    }
  }
});
```

---

**注意事项**:
1. 高德地图API有请求频率限制，注意控制调用频率
2. 位置权限需要用户授权，做好权限被拒绝的处理
3. 网络请求要有超时和错误处理机制
4. 跨平台开发优先使用uni-app标准API
5. API Key应通过配置文件管理，避免硬编码到代码中
# 配置文件使用指南

## 概述

本项目使用 `config.js` 文件来统一管理所有配置信息，包括API密钥、默认参数等。这种方式有以下优势：

- **安全性**: 避免API密钥硬编码在代码中
- **维护性**: 集中管理所有配置，便于修改
- **灵活性**: 不同环境可以使用不同的配置

## 配置文件结构

### 文件位置
```
项目根目录/config.js
```

### 主要配置项

#### 1. 高德地图API配置 (`amap`)
```javascript
amap: {
  key: 'your-amap-api-key',     // 高德地图Web API密钥
  baseUrl: 'https://restapi.amap.com',
  defaultSearch: {
    radius: 1000,               // 默认搜索半径（米）
    offset: 20,                 // 每页记录数
    page: 1,                    // 页码
    extensions: 'base'          // 返回结果详细程度
  }
}
```

#### 2. 地图配置 (`map`)
```javascript
map: {
  defaultCenter: {              // 默认地图中心点
    lat: 31.2304,              // 纬度（上海）
    lng: 121.4737              // 经度
  },
  defaultScale: 16,             // 默认缩放级别
  searchTypes: 'POI分类代码'    // 搜索的POI类型
}
```

#### 3. 应用配置 (`app`)
```javascript
app: {
  name: 'travel-recorder',
  version: '1.0.0',
  storageKeys: {
    travelData: 'travelData'    // 本地存储键名
  }
}
```

#### 4. 开发环境配置 (`dev`)
```javascript
dev: {
  debug: true,                  // 是否启用调试模式
  useMockData: false           // 是否使用模拟数据
}
```

## 使用方法

### 1. 在页面中引入配置
```javascript
import config from '../../config.js'

export default {
  data() {
    return {
      config: config  // 将配置添加到data中
    }
  }
}
```

### 2. 使用配置项
```javascript
methods: {
  // 使用API Key
  searchPOI() {
    uni.request({
      url: this.config.amap.baseUrl + '/v3/place/around',
      data: {
        key: this.config.amap.key,  // 从配置获取API Key
        radius: this.config.amap.defaultSearch.radius,
        // 其他参数...
      }
    })
  },
  
  // 使用默认地图中心点
  initMap() {
    this.mapCenter = {
      lat: this.config.map.defaultCenter.lat,
      lng: this.config.map.defaultCenter.lng
    }
  }
}
```

## 安全注意事项

### 1. API密钥保护
- **开发环境**: 可以直接在config.js中配置API密钥
- **生产环境**: 建议使用以下方式之一：
  - 环境变量
  - 服务器端代理
  - 动态获取（从服务器获取）

### 2. 版本控制
- 如果config.js包含敏感信息，考虑：
  - 创建config.example.js作为模板
  - 将config.js添加到.gitignore
  - 在部署时动态生成config.js

### 3. 生产环境配置示例
```javascript
// 生产环境可以这样处理API密钥
const config = {
  amap: {
    key: process.env.AMAP_API_KEY || 'fallback-key',
    // 其他配置...
  }
}
```

## 配置修改指南

### 1. 修改API密钥
1. 打开 `config.js` 文件
2. 找到 `amap.key` 配置项
3. 替换为你的高德地图API密钥
4. 保存文件

### 2. 修改默认地图中心点
1. 打开 `config.js` 文件
2. 找到 `map.defaultCenter` 配置项
3. 修改 `lat` 和 `lng` 值
4. 保存文件

### 3. 添加新的配置项
```javascript
// 在config.js中添加新的配置
const config = {
  // 现有配置...
  
  // 新增配置
  newFeature: {
    enabled: true,
    apiUrl: 'https://api.example.com',
    timeout: 5000
  }
}
```

## 常见问题

### Q: 如何在不同环境使用不同配置？
A: 可以创建多个配置文件：
- `config.dev.js` - 开发环境
- `config.prod.js` - 生产环境
- `config.test.js` - 测试环境

然后根据环境动态导入对应的配置文件。

### Q: API密钥泄露了怎么办？
A: 
1. 立即到高德地图控制台重新生成API密钥
2. 更新config.js中的密钥
3. 检查代码仓库历史，确保新密钥不会泄露

### Q: 配置文件导入失败？
A: 检查以下几点：
1. 文件路径是否正确
2. 是否正确导出配置对象
3. 是否有语法错误

---

**重要提醒**: 
- 定期检查和更新API密钥
- 不要将包含敏感信息的配置文件提交到公开仓库
- 在生产环境中使用更安全的密钥管理方案

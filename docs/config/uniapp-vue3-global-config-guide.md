# UniApp + Vue3 全局配置管理指南

## 问题背景

在 uniapp + Vue3 项目中，使用 ES6 `import/export` 导入配置文件时可能遇到兼容性问题，导致配置无法正确加载，出现类似 `Cannot read property 'amap' of undefined` 的错误。

## 解决方案

### 1. 统一配置文件架构

**文件位置**: `config.js`

```javascript
// 项目配置文件
const config = {
  // 高德地图Web API配置
  amap: {
    key: 'your-amap-api-key',
    baseUrl: 'https://restapi.amap.com',
    defaultSearch: {
      radius: 1000,
      offset: 20,
      page: 1,
      extensions: 'base'
    }
  },
  
  // 地图默认配置
  map: {
    defaultCenter: { lat: 31.2304, lng: 121.4737 },
    defaultScale: 16,
    searchTypes: '050000|060000|070000|080000|090000'
  },
  
  // 应用基础配置
  app: {
    name: 'travel-recorder',
    version: '1.0.0',
    storageKeys: {
      travelData: 'travelData'
    }
  }
};

// 多种导出方式确保兼容性
export default config;

// 兼容CommonJS导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = config;
}

// 全局配置挂载 - 关键解决方案
console.log('开始挂载全局配置...');

// 挂载到uni全局对象
if (typeof uni !== 'undefined') {
  uni.$appConfig = config;
  console.log('✅ 配置已挂载到 uni.$appConfig');
}

// 挂载到App全局数据
if (typeof getApp === 'function') {
  try {
    const app = getApp();
    if (app) {
      app.globalData = app.globalData || {};
      app.globalData.config = config;
      console.log('✅ 配置已挂载到 app.globalData.config');
    }
  } catch (e) {
    console.log('App实例尚未创建，稍后会自动挂载');
  }
}

// 挂载到window对象（H5环境）
if (typeof window !== 'undefined') {
  window.appConfig = config;
  console.log('✅ 配置已挂载到 window.appConfig');
}

console.log('全局配置挂载完成:', config);
```

### 2. 应用入口配置

**文件位置**: `main.js`

```javascript
import App from './App'
// 导入配置文件，自动挂载全局配置
import './config.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif
```

### 3. 组件中使用配置

**在 Vue 组件中获取配置**:

```javascript
export default {
  data() {
    return {
      config: null, // 配置将在 onLoad 中获取
      // 其他数据...
    }
  },
  
  onLoad(options) {
    // 从全局获取配置 - 多种备用方案
    try {
      this.config = uni.$appConfig || 
                   getApp()?.globalData?.config || 
                   window?.appConfig;
      
      console.log('从全局获取配置:', this.config);
    } catch (e) {
      console.error('获取全局配置失败:', e);
    }
    
    // 验证配置是否正确加载
    if (!this.config || !this.config.amap || !this.config.amap.key) {
      console.error('配置文件加载失败或配置不完整');
      uni.showModal({
        title: '配置错误',
        content: '配置文件加载失败，请检查 config.js 文件',
        showCancel: false
      });
      return;
    }
    
    // 使用配置
    this.initializeWithConfig();
  },
  
  methods: {
    // 使用配置中的API Key
    callAPI() {
      uni.request({
        url: this.config.amap.baseUrl + '/v3/place/around',
        data: {
          key: this.config.amap.key,
          // 其他参数...
        }
      });
    }
  }
}
```

## 核心解决思路

### 1. 避免直接 ES6 Import
- **问题**: uniapp 环境中 ES6 import 可能有兼容性问题
- **解决**: 在配置文件中自动挂载到全局对象，组件中从全局获取

### 2. 多层备用机制
```javascript
// 按优先级尝试多种获取方式
this.config = uni.$appConfig ||                    // uni全局对象
             getApp()?.globalData?.config ||       // App全局数据
             window?.appConfig;                     // window对象（H5）
```

### 3. 自动挂载策略
- 配置文件导入时自动执行挂载逻辑
- 支持多种环境（小程序、H5、App）
- 提供详细的调试信息

## 最佳实践

### ✅ 推荐做法
1. **统一配置文件**: 所有配置集中在 `config.js`
2. **避免硬编码**: 永远不要在代码中硬编码配置信息
3. **全局挂载**: 利用全局对象解决导入兼容性问题
4. **多重备用**: 提供多种配置获取方式确保可靠性
5. **错误处理**: 配置加载失败时给用户友好提示

### ❌ 避免做法
1. **硬编码配置**: 直接在组件中写死配置值
2. **单一导入方式**: 只依赖 ES6 import
3. **忽略错误处理**: 不检查配置是否正确加载
4. **分散配置**: 配置信息散布在多个文件中

## 调试技巧

### 1. 控制台输出检查
运行项目后查看控制台，应该看到：
```
开始挂载全局配置...
✅ 配置已挂载到 uni.$appConfig
✅ 配置已挂载到 app.globalData.config
全局配置挂载完成: {amap: {...}, map: {...}}
从全局获取配置: {amap: {...}, map: {...}}
```

### 2. 常见问题排查
- **配置为 undefined**: 检查 `main.js` 是否正确导入 `config.js`
- **API Key 无效**: 检查 `config.js` 中的 API Key 是否正确
- **权限错误**: 检查 `manifest.json` 中的权限配置

## 兼容性说明

此方案适用于：
- ✅ uniapp + Vue3
- ✅ 微信小程序
- ✅ H5
- ✅ App
- ✅ HBuilderX IDE

## 总结

通过将配置文件自动挂载到全局对象，我们解决了 uniapp + Vue3 环境中 ES6 import 的兼容性问题，实现了：

1. **可靠的配置管理**: 多重备用机制确保配置始终可用
2. **良好的开发体验**: 统一的配置获取方式
3. **易于维护**: 单一配置文件，避免硬编码
4. **跨平台兼容**: 支持所有 uniapp 目标平台

这种方案既保持了代码的整洁性，又解决了实际的兼容性问题，是 uniapp + Vue3 项目中全局配置管理的最佳实践。

# 技术实现详解 - AI编辑器协作指南

## 核心文件结构与职责

### 主要页面文件
```
pages/
├── index/index.vue          # 主页 - 旅行列表展示
├── record/record.vue        # 记录页 - 单个旅行点管理
├── city/city.vue           # 城市选择页
├── smart-input/smart-input.vue  # 智能输入核心页面
├── detail/detail.vue       # 详情展示页
├── stats/stats.vue         # 统计分析页
├── share/share.vue         # 分享功能页
├── search/search.vue       # 搜索页面
└── map/map.vue             # 地图查看页面
```

### 配置文件
- `pages.json` - 页面路由配置
- `manifest.json` - 应用配置，包含权限设置
- `config.js` - 项目配置文件，管理API密钥等
- `uni.scss` - 全局样式

## 关键代码实现模式

### 1. 数据管理模式

**数据初始化模式** (所有页面通用):
```javascript
import config from '../../config.js'

export default {
  data() {
    return {
      config: config,          // 配置文件
      travel: { travel: [] },  // 主数据结构
      currentCityIndex: 0,     // 当前城市索引
      currentPointIndex: -1    // 当前点索引，-1表示新增
    }
  },
  onLoad(options) {
    // 获取传递参数
    if (options.cityId !== undefined) {
      this.currentCityIndex = parseInt(options.cityId);
    }
    if (options.pointIndex !== undefined) {
      this.currentPointIndex = parseInt(options.pointIndex);
    }
    
    // 加载数据
    this.loadData();
  },
  methods: {
    loadData() {
      this.travel = uni.getStorageSync('travelData') || { travel: [] };
    },
    saveData() {
      uni.setStorageSync('travelData', this.travel);
    }
  }
}
```

### 2. 页面导航模式

**标准导航传参**:
```javascript
// 导航到智能输入页面
goToSmartInput() {
  uni.navigateTo({
    url: `/pages/smart-input/smart-input?cityId=${this.currentCityIndex}&pointIndex=${this.currentPointIndex}`
  });
}

// 返回并传递数据
navigateBackWithData(data) {
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  if (prevPage) {
    prevPage.$vm.receiveData && prevPage.$vm.receiveData(data);
  }
  uni.navigateBack();
}
```

### 3. 智能输入系统实现

**核心组件结构** (`smart-input.vue`):
```javascript
export default {
  data() {
    return {
      currentMethod: 'location', // 输入模式: location|text|voice|image
      mapContext: null,          // 地图上下文
      mapCenter: {               // 当前地图中心
        lat: 31.2304,
        lng: 121.4737
      },
      nearbyPOIs: [],           // POI列表
      selectedPOI: null,        // 选中的POI
      isLoadingPOI: false       // 加载状态
    }
  },
  
  onLoad(options) {
    this.cityId = options.cityId;
    this.pointIndex = options.pointIndex;
    this.getCurrentLocation();
  },
  
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.searchPOI(res.longitude, res.latitude);
        }
      });
    },
    
    // POI搜索实现
    searchNearbyPOIs() {
      this.isLoadingPOI = true;

      uni.request({
        url: 'https://restapi.amap.com/v3/place/around',
        method: 'GET',
        data: {
          key: this.config.amapKey, // 使用配置文件中的API Key
          location: `${this.mapCenter.lng},${this.mapCenter.lat}`,
          radius: 1000,
          types: '050000|060000|070000|080000|090000|100000|110000|120000|130000|140000|150000|160000|170000|180000|190000|200000',
          offset: 20,
          page: 1,
          extensions: 'base'
        },
        success: (res) => {
          if (res.data.status === '1' && res.data.pois && res.data.pois.length > 0) {
            this.nearbyPOIs = res.data.pois.map((poi, index) => ({
              id: index + 1,
              name: poi.name,
              address: poi.address,
              type: poi.type,
              distance: Math.round(parseFloat(poi.distance) || 0),
              lat: parseFloat(poi.location.split(',')[1]),
              lng: parseFloat(poi.location.split(',')[0])
            }));
            uni.showToast({ title: `找到${this.nearbyPOIs.length}个附近地点`, icon: 'success' });
          } else {
            this.loadFallbackPOIs();
          }
        },
        fail: (err) => {
          console.error('高德Web API调用失败:', err);
          uni.showToast({ title: '网络错误，使用默认数据', icon: 'none' });
          this.loadFallbackPOIs();
        },
        complete: () => {
          this.isLoadingPOI = false;
        }
      });
    },
    
    // 地图区域变化监听
    onRegionChange(e) {
      if (e.type === 'end') {
        const mapContext = uni.createMapContext('smartInputMap', this);
        mapContext.getCenterLocation({
          success: (res) => {
            this.mapCenter = {
              lat: res.latitude,
              lng: res.longitude
            };
            this.searchNearbyPOIs();
          },
          fail: (err) => {
            console.error('获取地图中心点失败:', err);
          }
        });
      }
    },
    
    // 选择POI
    selectPOI(poi) {
      this.selectedPOI = poi;
      const pointData = {
        place: poi.name,
        lat: parseFloat(poi.location.split(',')[1]),
        lng: parseFloat(poi.location.split(',')[0]),
        desc: poi.address || '',
        arriveTime: '',
        duration: '',
        media: [],
        experience: 0,
        expenses: []
      };
      this.navigateBackWithData(pointData);
    }
  }
}
```

### 4. 地图组件配置

**标准地图配置**:
```html
<map
  id="smartInputMap"
  :latitude="mapCenter.lat"
  :longitude="mapCenter.lng"
  :markers="markers"
  :show-location="true"
  :scale="16"
  style="width: 100%; height: 400rpx;"
  @markertap="onMarkerTap"
  @callouttap="onMarkerTap"
  @regionchange="onRegionChange"
/>
```

### 5. 数据验证与错误处理模式

**标准错误处理**:
```javascript
methods: {
  async performAPICall() {
    uni.showLoading({ title: '加载中...' });
    try {
      const result = await this.apiFunction();
      this.handleSuccess(result);
    } catch (error) {
      console.error('API调用失败:', error);
      uni.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 2000
      });
    } finally {
      uni.hideLoading();
    }
  },
  
  validatePointData(point) {
    if (!point.place || point.place.trim() === '') {
      uni.showToast({ title: '请填写地点名称', icon: 'none' });
      return false;
    }
    return true;
  }
}
```

## 样式规范

### 响应式设计模式
```scss
// 使用rpx单位
.container {
  padding: 30rpx;
  
  .item {
    margin-bottom: 20rpx;
    padding: 24rpx;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  }
  
  .btn-primary {
    background: #007aff;
    color: #fff;
    border-radius: 8rpx;
    padding: 20rpx 40rpx;
  }
}

// 状态样式
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.selected {
  border: 2rpx solid #007aff;
  background: #f0f8ff;
}
```

## 配置管理

### 配置文件结构 (`config.js`)
```javascript
import config from '../../config.js'

// 配置文件包含以下主要部分：
const config = {
  amap: {
    key: 'your-amap-api-key',     // 高德地图API密钥
    baseUrl: 'https://restapi.amap.com',
    defaultSearch: {
      radius: 1000,               // 默认搜索半径
      offset: 20,                 // 每页记录数
      page: 1,                    // 页码
      extensions: 'base'          // 返回结果详细程度
    }
  },
  map: {
    defaultCenter: { lat: 31.2304, lng: 121.4737 }, // 默认中心点
    defaultScale: 16,             // 默认缩放级别
    searchTypes: 'POI分类代码'    // 搜索类型
  },
  app: {
    storageKeys: {
      travelData: 'travelData'    // 数据存储键名
    }
  }
}
```

### 配置文件使用方法
```javascript
// 在页面中引入配置
import config from '../../config.js'

export default {
  data() {
    return {
      config: config  // 将配置添加到data中
    }
  },
  methods: {
    // 使用配置中的API Key
    callAPI() {
      uni.request({
        url: this.config.amap.baseUrl + '/v3/place/around',
        data: {
          key: this.config.amap.key,  // 从配置获取API Key
          // 其他参数...
        }
      })
    }
  }
}
```

## API集成规范

### 高德地图API调用模式
```javascript
// 标准API调用封装
class AmapAPI {
  constructor(config) {
    this.baseURL = config.amap.baseUrl;
    this.key = config.amap.key;
  }
  
  async searchPOI(params) {
    const response = await uni.request({
      url: `${this.baseURL}/v3/place/around`,
      data: {
        key: this.key,
        ...params
      }
    });

    if (response.data.status !== '1') {
      throw new Error(response.data.info || 'API调用失败');
    }

    return response.data;
  }

  async geocode(address) {
    const response = await uni.request({
      url: `${this.baseURL}/v3/geocode/geo`,
      data: {
        key: this.key,
        address: address
      }
    });

    return response.data;
  }
}
```

## 组件通信模式

### 父子组件数据传递
```javascript
// 父组件调用子组件方法
this.$refs.childComponent.methodName(params);

// 子组件向父组件传递数据
this.$emit('custom-event', data);

// 页面间数据传递（已实现）
navigateBackWithData(data) {
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  if (prevPage && prevPage.$vm.receiveData) {
    prevPage.$vm.receiveData(data);
  }
  uni.navigateBack();
}
```

## 调试与测试

### 开发环境配置
- 在HBuilderX中运行到微信开发者工具
- 启用调试模式查看控制台日志
- 使用微信开发者工具的网络面板监控API调用

### 常见问题处理
1. **地图不显示**: 检查manifest.json权限配置
2. **POI搜索失败**: 验证网络连接和API Key配置
3. **页面跳转数据丢失**: 确认getCurrentPages()调用时机
4. **样式兼容性**: 使用rpx单位，避免固定像素
5. **API Key配置**: 确保config.js文件正确配置并导入

---

**开发提示**: 
- 保持跨平台兼容性，优先使用uni-app标准API
- 所有异步操作都要有错误处理
- 使用console.log进行调试，生产环境需移除
- 数据变更后及时调用saveData()保存到本地存储 
<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */

	/* 小程序全屏样式 */
	page {
		height: 100%;
		background: #EDF7F3;
	}

	/* 全局容器样式 */
	.container {
		min-height: 100vh;
		box-sizing: border-box;
	}

	/* 确保在小程序环境下正确显示 */
	#app {
		height: 100%;
	}

	/* 全局按钮样式重置 */
	button {
		font-size: 28rpx;
		white-space: nowrap;
		border-radius: 10rpx;
		overflow: unset;
		line-height: 2;
		padding: 0;
		margin: 0;
		border: none;
		background: transparent;
		color: inherit;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 移除按钮的默认边框 */
	button::after {
		border: none;
	}
</style>
<style>
/* 导入字体图标 替换成你的自己路径 */  
@import './static/all.css';
</style>